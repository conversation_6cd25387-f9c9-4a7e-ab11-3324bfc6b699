#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API队列监控和管理工具
用于监控和管理API队列积压问题
"""

import time
import logging
import threading
from datetime import datetime, timedelta
import json

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class APIQueueMonitor:
    def __init__(self):
        self.monitoring = False
        self.stats_history = []
        self.max_history = 100
        
    def start_monitoring(self, tcp_handler, interval=5):
        """开始监控API队列"""
        self.monitoring = True
        self.tcp_handler = tcp_handler
        
        def monitor_loop():
            while self.monitoring:
                try:
                    stats = self.collect_stats()
                    self.analyze_stats(stats)
                    self.stats_history.append(stats)
                    
                    # 保持历史记录数量
                    if len(self.stats_history) > self.max_history:
                        self.stats_history.pop(0)
                    
                    time.sleep(interval)
                except Exception as e:
                    logging.error(f"监控异常: {str(e)}")
                    time.sleep(interval)
        
        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()
        logging.info("API队列监控已启动")
    
    def collect_stats(self):
        """收集统计信息"""
        try:
            with self.tcp_handler.api_batch_mutex:
                api_queue_size = len(self.tcp_handler.api_batch_queue)
            
            with self.tcp_handler.retry_mutex:
                retry_queue_size = len(self.tcp_handler.retry_queue)
            
            with self.tcp_handler.update_buffer_mutex:
                ui_buffer_size = len(self.tcp_handler.update_buffer)
            
            with self.tcp_handler.stats_mutex:
                total_messages = self.tcp_handler.stats['total_messages']
                failed_messages = self.tcp_handler.stats['failed_messages']
                messages_per_second = self.tcp_handler.stats['messages_per_second']
                active_connections = self.tcp_handler.stats['active_connections']
            
            return {
                'timestamp': datetime.now(),
                'api_queue_size': api_queue_size,
                'retry_queue_size': retry_queue_size,
                'ui_buffer_size': ui_buffer_size,
                'total_messages': total_messages,
                'failed_messages': failed_messages,
                'messages_per_second': messages_per_second,
                'active_connections': active_connections,
                'success_rate': ((total_messages - failed_messages) / total_messages * 100) if total_messages > 0 else 0
            }
        except Exception as e:
            logging.error(f"收集统计信息失败: {str(e)}")
            return None
    
    def analyze_stats(self, stats):
        """分析统计信息并提供建议"""
        if not stats:
            return
        
        api_queue_size = stats['api_queue_size']
        messages_per_second = stats['messages_per_second']
        success_rate = stats['success_rate']
        
        # 队列积压分析
        if api_queue_size > 5000:
            logging.error(f"🚨 严重警告: API队列积压 {api_queue_size} 条，建议立即处理")
            self.suggest_emergency_actions(stats)
        elif api_queue_size > 2000:
            logging.warning(f"⚠️  警告: API队列积压 {api_queue_size} 条，需要关注")
            self.suggest_optimization(stats)
        elif api_queue_size > 1000:
            logging.info(f"ℹ️  提示: API队列 {api_queue_size} 条，轻微积压")
        
        # 成功率分析
        if success_rate < 80:
            logging.warning(f"⚠️  API成功率较低: {success_rate:.1f}%，检查API服务器状态")
        
        # 处理速度分析
        if messages_per_second > 50:
            logging.info(f"📈 高频处理: {messages_per_second:.1f} msg/s")
        
        # 定期输出详细统计
        if len(self.stats_history) % 12 == 0:  # 每分钟输出一次详细信息
            self.print_detailed_stats(stats)
    
    def suggest_emergency_actions(self, stats):
        """建议紧急处理措施"""
        suggestions = [
            "1. 检查API服务器是否正常响应",
            "2. 考虑临时增加API_THREADS数量",
            "3. 减少API_BATCH_INTERVAL到500ms",
            "4. 检查网络连接状况",
            "5. 考虑启用紧急清理机制"
        ]
        
        logging.error("🆘 紧急处理建议:")
        for suggestion in suggestions:
            logging.error(f"   {suggestion}")
    
    def suggest_optimization(self, stats):
        """建议优化措施"""
        suggestions = []
        
        if stats['messages_per_second'] > 30:
            suggestions.append("消息频率较高，考虑增加API_BATCH_SIZE到300")
        
        if stats['success_rate'] < 90:
            suggestions.append("API成功率偏低，检查API服务器性能")
        
        if stats['retry_queue_size'] > 50:
            suggestions.append("重试队列积压，检查重试机制")
        
        suggestions.append("考虑减少API_BATCH_INTERVAL到2000ms")
        suggestions.append("监控API服务器响应时间")
        
        if suggestions:
            logging.warning("💡 优化建议:")
            for suggestion in suggestions:
                logging.warning(f"   • {suggestion}")
    
    def print_detailed_stats(self, stats):
        """打印详细统计信息"""
        logging.info("=" * 60)
        logging.info("📊 详细统计报告:")
        logging.info(f"   API队列大小: {stats['api_queue_size']}")
        logging.info(f"   重试队列大小: {stats['retry_queue_size']}")
        logging.info(f"   UI缓冲区大小: {stats['ui_buffer_size']}")
        logging.info(f"   活跃连接数: {stats['active_connections']}")
        logging.info(f"   总消息数: {stats['total_messages']}")
        logging.info(f"   失败消息数: {stats['failed_messages']}")
        logging.info(f"   成功率: {stats['success_rate']:.1f}%")
        logging.info(f"   处理速率: {stats['messages_per_second']:.1f} msg/s")
        
        # 趋势分析
        if len(self.stats_history) >= 5:
            self.analyze_trends()
        
        logging.info("=" * 60)
    
    def analyze_trends(self):
        """分析趋势"""
        recent_stats = self.stats_history[-5:]
        
        # 队列大小趋势
        queue_sizes = [s['api_queue_size'] for s in recent_stats]
        if len(set(queue_sizes)) > 1:  # 有变化
            if queue_sizes[-1] > queue_sizes[0]:
                logging.warning("📈 趋势: API队列持续增长")
            else:
                logging.info("📉 趋势: API队列正在减少")
        
        # 处理速率趋势
        rates = [s['messages_per_second'] for s in recent_stats]
        avg_rate = sum(rates) / len(rates)
        logging.info(f"📊 平均处理速率: {avg_rate:.1f} msg/s")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        logging.info("API队列监控已停止")
    
    def export_stats(self, filename=None):
        """导出统计数据"""
        if not filename:
            filename = f"api_stats_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            # 转换datetime对象为字符串
            export_data = []
            for stat in self.stats_history:
                stat_copy = stat.copy()
                stat_copy['timestamp'] = stat_copy['timestamp'].isoformat()
                export_data.append(stat_copy)
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            logging.info(f"统计数据已导出到: {filename}")
        except Exception as e:
            logging.error(f"导出统计数据失败: {str(e)}")

def main():
    print("API队列监控工具")
    print("此工具需要与运行中的TCP服务器配合使用")
    print("请确保服务器已启动后再运行此工具")
    
    # 这里需要获取TCP服务器实例
    # 在实际使用中，你需要从主程序中传入tcp_handler实例
    print("请在主程序中集成此监控工具")

if __name__ == "__main__":
    main()
