#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据流监控工具
用于监控TCP数据接收的连续性和间隔问题
"""

import socket
import time
import threading
import logging
from datetime import datetime
import matplotlib.pyplot as plt
from collections import deque
import numpy as np

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class DataFlowMonitor:
    def __init__(self, server_host='localhost', server_port=2000):
        self.server_host = server_host
        self.server_port = server_port
        self.monitoring = False
        self.data_timestamps = deque(maxlen=1000)
        self.data_sizes = deque(maxlen=1000)
        self.gaps = []
        
    def start_monitoring(self, duration=60):
        """开始监控数据流"""
        print(f"开始监控数据流 {duration} 秒...")
        self.monitoring = True
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.connect((self.server_host, self.server_port))
            sock.settimeout(1.0)
            
            start_time = time.time()
            last_data_time = None
            total_bytes = 0
            data_count = 0
            
            print("连接成功，开始监控...")
            
            while time.time() - start_time < duration and self.monitoring:
                try:
                    data = sock.recv(1024)
                    if not data:
                        print("连接已关闭")
                        break
                    
                    current_time = time.time()
                    data_size = len(data)
                    total_bytes += data_size
                    data_count += 1
                    
                    # 记录时间戳和数据大小
                    self.data_timestamps.append(current_time)
                    self.data_sizes.append(data_size)
                    
                    # 计算数据间隔
                    if last_data_time:
                        gap = current_time - last_data_time
                        if gap > 0.5:  # 超过0.5秒的间隔
                            self.gaps.append(gap)
                            print(f"数据间隔: {gap:.2f}秒")
                    
                    last_data_time = current_time
                    
                    # 实时显示
                    if data_count % 10 == 0:
                        elapsed = current_time - start_time
                        rate = total_bytes / elapsed if elapsed > 0 else 0
                        print(f"已接收: {data_count} 次, {total_bytes} 字节, 速率: {rate:.1f} B/s")
                    
                except socket.timeout:
                    current_time = time.time()
                    if last_data_time:
                        gap = current_time - last_data_time
                        if gap > 2.0:  # 超过2秒无数据
                            print(f"⚠️  数据中断: {gap:.1f}秒")
                            self.gaps.append(gap)
                    continue
                except Exception as e:
                    print(f"接收数据异常: {e}")
                    break
            
            # 输出统计结果
            self.print_statistics(duration, total_bytes, data_count)
            
        except Exception as e:
            print(f"连接失败: {e}")
        finally:
            try:
                sock.close()
            except:
                pass
    
    def print_statistics(self, duration, total_bytes, data_count):
        """打印统计结果"""
        print("\n" + "="*60)
        print("📊 数据流监控统计报告")
        print("="*60)
        print(f"监控时长: {duration} 秒")
        print(f"总接收数据: {total_bytes} 字节 ({total_bytes/1024:.1f} KB)")
        print(f"接收次数: {data_count}")
        print(f"平均速率: {total_bytes/duration:.1f} B/s")
        
        if self.gaps:
            print(f"\n数据间隔统计:")
            print(f"  间隔次数: {len(self.gaps)}")
            print(f"  最大间隔: {max(self.gaps):.2f} 秒")
            print(f"  平均间隔: {np.mean(self.gaps):.2f} 秒")
            print(f"  间隔总时长: {sum(self.gaps):.2f} 秒")
            
            # 分析间隔模式
            short_gaps = [g for g in self.gaps if g < 2.0]
            long_gaps = [g for g in self.gaps if g >= 2.0]
            
            print(f"\n间隔分析:")
            print(f"  短间隔(<2s): {len(short_gaps)} 次")
            print(f"  长间隔(≥2s): {len(long_gaps)} 次")
            
            if long_gaps:
                print(f"  ⚠️  发现 {len(long_gaps)} 次长时间数据中断")
        else:
            print("✅ 数据流连续，无明显间隔")
        
        # 数据接收模式分析
        if len(self.data_timestamps) > 1:
            intervals = []
            for i in range(1, len(self.data_timestamps)):
                interval = self.data_timestamps[i] - self.data_timestamps[i-1]
                intervals.append(interval)
            
            if intervals:
                print(f"\n接收模式分析:")
                print(f"  平均接收间隔: {np.mean(intervals):.3f} 秒")
                print(f"  接收间隔标准差: {np.std(intervals):.3f} 秒")
                
                # 判断接收模式
                if np.std(intervals) < 0.1:
                    print("  📈 接收模式: 稳定连续")
                elif np.std(intervals) < 0.5:
                    print("  📊 接收模式: 轻微波动")
                else:
                    print("  📉 接收模式: 不规律/间歇性")
        
        print("="*60)
    
    def plot_data_flow(self):
        """绘制数据流图表"""
        if len(self.data_timestamps) < 2:
            print("数据不足，无法绘制图表")
            return
        
        try:
            # 转换为相对时间
            start_time = self.data_timestamps[0]
            times = [(t - start_time) for t in self.data_timestamps]
            
            # 创建图表
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
            
            # 数据大小图
            ax1.plot(times, list(self.data_sizes), 'b-', alpha=0.7)
            ax1.set_title('数据接收大小随时间变化')
            ax1.set_xlabel('时间 (秒)')
            ax1.set_ylabel('数据大小 (字节)')
            ax1.grid(True, alpha=0.3)
            
            # 接收间隔图
            if len(times) > 1:
                intervals = []
                interval_times = []
                for i in range(1, len(times)):
                    interval = times[i] - times[i-1]
                    intervals.append(interval)
                    interval_times.append(times[i])
                
                ax2.plot(interval_times, intervals, 'r-', alpha=0.7)
                ax2.set_title('数据接收间隔')
                ax2.set_xlabel('时间 (秒)')
                ax2.set_ylabel('间隔 (秒)')
                ax2.grid(True, alpha=0.3)
                
                # 标记长间隔
                for i, interval in enumerate(intervals):
                    if interval > 1.0:
                        ax2.plot(interval_times[i], interval, 'ro', markersize=8)
            
            plt.tight_layout()
            plt.savefig(f'data_flow_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png')
            plt.show()
            
        except Exception as e:
            print(f"绘制图表失败: {e}")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False

def main():
    monitor = DataFlowMonitor()
    
    print("数据流监控工具")
    print("1. 开始监控 (60秒)")
    print("2. 自定义监控时长")
    print("3. 退出")
    
    choice = input("请选择操作 (1-3): ").strip()
    
    if choice == "1":
        monitor.start_monitoring(60)
        
        plot_choice = input("是否绘制数据流图表? (y/n): ").strip().lower()
        if plot_choice == 'y':
            monitor.plot_data_flow()
            
    elif choice == "2":
        try:
            duration = int(input("监控时长(秒): "))
            monitor.start_monitoring(duration)
            
            plot_choice = input("是否绘制数据流图表? (y/n): ").strip().lower()
            if plot_choice == 'y':
                monitor.plot_data_flow()
        except ValueError:
            print("输入格式错误")
    elif choice == "3":
        print("退出")
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
