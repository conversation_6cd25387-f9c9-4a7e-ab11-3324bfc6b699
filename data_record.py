# 数据记录模块：定义DataRecord类，用于封装芯片扫描数据及上传状态
# 该类包含芯片编号、设备编号、扫描时间、上传状态、重试次数和最后重试时间等属性
from datetime import datetime


class DataRecord:
    """数据记录容器类"""
    __slots__ = ['chip_id', 'device_id', 'scan_time', 'status', 'retry_count', 'last_retry']

    def __init__(self, chip_id, device_id, scan_time):
        self.chip_id = str(202501) + chip_id
        self.device_id = device_id
        self.scan_time = scan_time
        self.status = "待上传"
        self.retry_count = 0
        self.last_retry = datetime.min

    def to_dict(self):
        """将DataRecord对象转换为字典格式"""
        return {
            "chip_id": self.chip_id,
            "device_id": self.device_id,
            "scan_time": self.scan_time,
            "status": self.status,
            "retry_count": self.retry_count,
            "last_retry": self.last_retry.isoformat() if self.last_retry else None
        }