# 配置模块：用于集中管理系统运行所需的常量参数
# 包括端口号、消息长度、API地址、重试次数、请求超时和重试间隔等
import os
PORT = 2000
MESSAGE_LENGTH = 40
API_URL = "http://gameno.jatimer.com:8033/Handling/hcacceptData"
MAX_RETRY_ATTEMPTS = 3
REQUEST_TIMEOUT = 5
RETRY_INTERVAL = 20
DB_PATH = os.path.join(os.path.dirname(__file__), "data", "data.db")
MAX_UI_RECORDS = 100  # 每次UI更新处理的最大记录数
MAX_MEMORY_RECORDS = 10000000

# 网络优化配置
MAX_CONNECTIONS = 100  # 最大并发连接数
SOCKET_RECV_BUFFER = 64 * 1024  # 64KB socket接收缓冲区 (减小以提高响应性)
SOCKET_SEND_BUFFER = 64 * 1024  # 64KB socket发送缓冲区
CONNECTION_TIMEOUT = 5.0  # 连接超时时间（秒）(减少到5秒)
RECV_TIMEOUT = 1.0  # 接收数据超时时间（秒）
MAX_CONSECUTIVE_ERRORS = 5  # 最大连续错误次数
MESSAGE_RECEIVE_TIMEOUT = 5.0  # 消息接收超时时间（秒）

# 线程池配置
NETWORK_THREADS = 20  # 网络处理线程数
DATA_PROCESSING_THREADS = 30  # 数据处理线程数
API_THREADS = 15  # API处理线程数
RETRY_THREADS = 5  # 重试处理线程数

# 批量处理配置
API_BATCH_SIZE = 200  # API批量发送大小 (增加到200)
API_BATCH_INTERVAL = 1000  # API批量发送间隔（毫秒）(减少到1秒)
API_MAX_QUEUE_SIZE = 5000  # API队列最大大小，超过则丢弃旧数据
UI_UPDATE_INTERVAL = 50  # UI更新间隔（毫秒）- 减少到50ms提高响应性
STATS_LOG_INTERVAL = 10000  # 统计日志间隔（毫秒）

# 调试配置
DEBUG_MESSAGE_FORMAT = True  # 是否启用消息格式调试
MAX_BUFFER_SIZE = 8192  # 最大缓冲区大小，防止内存泄漏