from PyQt5.QtWidgets import QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QTreeWidget, QTreeWidgetItem, QMessageBox, QFrame, QHeaderView, QShortcut
from PyQt5.QtCore import Qt, QTimer, QMutex, QMutexLocker, QThread, pyqtSignal
from PyQt5.QtGui import QIcon, QColor, QKeySequence
from tcp_server_handler import TcpServerHandler
from config import PORT, MAX_UI_RECORDS
import logging
from collections import deque

# 主窗口模块：基于PyQt5实现的主界面，负责UI布局、用户交互、数据显示与刷新、服务控制等
# 包含MainWindow类，集成了界面初始化、统计信息、设备列表、数据表格等功能

class ServerStopThread(QThread):
    """后台停止服务线程，避免UI阻塞"""
    finished_signal = pyqtSignal(dict)  # 发送停止完成信号和统计信息
    progress_signal = pyqtSignal(str)   # 发送进度信息

    def __init__(self, server_handler, main_window):
        super().__init__()
        self.server_handler = server_handler
        self.main_window = main_window

    def run(self):
        """在后台线程中执行停止操作"""
        try:
            self.progress_signal.emit("📊 统计停止前状态...")

            # 统计停止前的状态
            total_items = self.main_window.tree.topLevelItemCount()
            pending_count = 0
            success_count = 0
            failed_count = 0

            for i in range(total_items):
                item = self.main_window.tree.topLevelItem(i)
                if item:
                    status = item.text(3)
                    if status == "待上传":
                        pending_count += 1
                    elif status == "已成功":
                        success_count += 1
                    elif status == "已失败":
                        failed_count += 1

            before_stats = {
                'total': total_items,
                'pending': pending_count,
                'success': success_count,
                'failed': failed_count
            }

            logging.info("=" * 60)
            logging.info("🛑 开始停止服务")
            logging.info(f"📊 停止前状态统计:")
            logging.info(f"   总记录数: {total_items}")
            logging.info(f"   待上传: {pending_count}")
            logging.info(f"   已成功: {success_count}")
            logging.info(f"   已失败: {failed_count}")
            logging.info("=" * 60)

            self.progress_signal.emit("🔄 处理剩余UI数据...")

            # 处理剩余的UI缓冲区数据
            self.main_window._process_and_refresh_ui()
            logging.info("✅ 已处理剩余的UI数据")

            self.progress_signal.emit("🛑 停止服务器...")

            # 停止服务器
            self.server_handler.stop()

            self.progress_signal.emit("⏳ 等待状态更新...")

            # 等待状态更新完成
            import time
            time.sleep(3)  # 等待状态更新

            self.progress_signal.emit("🔧 最终处理...")

            # 强制处理UI更新
            self.main_window._process_and_refresh_ui()
            time.sleep(1)

            # 统计停止后的状态
            pending_count_after = 0
            success_count_after = 0
            failed_count_after = 0

            for i in range(total_items):
                item = self.main_window.tree.topLevelItem(i)
                if item:
                    status = item.text(3)
                    if status == "待上传":
                        pending_count_after += 1
                    elif status == "已成功":
                        success_count_after += 1
                    elif status == "已失败":
                        failed_count_after += 1

            after_stats = {
                'pending': pending_count_after,
                'success': success_count_after,
                'failed': failed_count_after
            }

            # 发送完成信号
            self.finished_signal.emit({
                'before': before_stats,
                'after': after_stats
            })

        except Exception as e:
            logging.error(f"后台停止服务异常: {str(e)}")
            import traceback
            logging.error(f"详细错误: {traceback.format_exc()}")
            self.finished_signal.emit({'error': str(e)})
class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        # 设置窗口左上角图标
        self.setWindowIcon(QIcon("hechuang.ico"))
        self.server_handler = None
        self.data_items = {}

        # 后台停止线程
        self.stop_thread = None

        # UI刷新优化相关变量
        self.update_buffer = deque()
        self.buffer_mutex = QMutex()
        self.last_total_scans = 0
        self.last_unique_chips = 0
        self.last_device_list_text = ""

        # 缓冲区监控
        self.buffer_stats = {
            'max_size': 0,
            'total_cleared': 0,
            'last_clear_time': None
        }

        self.tree = QTreeWidget()
        self.tree.setHeaderLabels(["芯片编号", "设备编号", "扫描时间", "上传状态"])
        self.tree.setStyleSheet("""
            QTreeWidget {
                font-size: 13px;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                background: white;
            }
            QTreeWidget::item {
                border-bottom: 1px solid #eee;
                padding: 10px 0;
            }
            QTreeWidget::item:selected {
                background: #2196F3;
                color: white;
            }
        """)
        self.tree.header().setStyleSheet("""
            QHeaderView::section {
                background-color: #607D8B;
                color: white;
                padding: 12px;
                border: none;
                font-size: 13px;
            }
        """)
        self.tree.header().setSectionResizeMode(QHeaderView.Stretch)
        self.tree.setAlternatingRowColors(True)

        self._init_ui()
        self._init_timers()
        self._init_shortcuts()

    def _init_ui(self):
        self.setWindowTitle("合创体育 v4.0.2")
        self.setGeometry(100, 100, 1280, 720)

        # 主容器
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)

        # ===== 状态栏 =====
        status_frame = QFrame()
        status_layout = QHBoxLayout(status_frame)
        status_layout.setContentsMargins(10, 5, 10, 5)

        self.server_status = QLabel("🔴 服务未运行")
        self.server_status.setStyleSheet("font-size: 14px; color: #666;")
        self.port_label = QLabel(f"端口：{PORT}")
        self.port_label.setStyleSheet("font-size: 14px; color: #666;")

        status_layout.addWidget(self.server_status)
        status_layout.addStretch()
        status_layout.addWidget(self.port_label)
        main_layout.addWidget(status_frame)

        # ===== 统计面板 =====
        stats_frame = QFrame()
        stats_layout = QHBoxLayout(stats_frame)
        stats_layout.setSpacing(20)

        self.scan_count = self._create_stat_card("0", "总扫描次数", "#4CAF50")
        self.participant_count = self._create_stat_card("0", "人数", "#2196F3")
        self.device_count = self._create_stat_card("0", "在线设备", "#9C27B0")

        stats_layout.addWidget(self.scan_count)
        stats_layout.addWidget(self.participant_count)
        stats_layout.addWidget(self.device_count)
        main_layout.addWidget(stats_frame)

        # ===== 数据表格 =====
        self.tree.setStyleSheet("""
            QTreeWidget {
                font-size: 13px;
            }
            QTreeWidget::item {
                height: 28px;
            }
        """)
        self.tree.header().setSectionResizeMode(QHeaderView.Stretch)
        self.tree.setAlternatingRowColors(True)

        # 优化树形控件性能
        self.tree.setUniformRowHeights(True)  # 统一行高，提高性能
        self.tree.setRootIsDecorated(False)  # 不显示根节点装饰
        self.tree.setSortingEnabled(False)  # 禁用排序，提高性能
        main_layout.addWidget(self.tree)

        # ===== 控制面板 =====
        control_frame = QFrame()
        control_layout = QHBoxLayout(control_frame)
        self.start_btn = QPushButton("启动服务")
        self.start_btn.setFixedSize(120, 40)
        self.start_btn.setStyleSheet("""
            QPushButton {
                background: #4CAF50;
                color: white;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background: #45a049;
            }
        """)
        self.start_btn.clicked.connect(self.toggle_server)
        control_layout.addWidget(self.start_btn)
        main_layout.addWidget(control_frame)

        # ===== 设备面板 =====
        device_frame = QFrame()
        device_layout = QVBoxLayout(device_frame)
        device_layout.setContentsMargins(10, 10, 10, 10)

        title = QLabel("📡 在线设备列表")
        title.setStyleSheet("font-size: 16px; color: #333;")
        self.device_list = QLabel("无活动连接")
        self.device_list.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #666;
                padding: 10px;
                background: #f8f9fa;
                border-radius: 6px;
                min-height: 60px;
            }
        """)
        self.device_list.setWordWrap(True)

        device_layout.addWidget(title)
        device_layout.addWidget(self.device_list)
        main_layout.addWidget(device_frame)

    def _create_stat_card(self, value, title, color):
        """创建统计卡片"""
        frame = QFrame()
        frame.setStyleSheet(f"""
            background: white;
            border-radius: 8px;
            border: 1px solid #eee;
        """)
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(20, 15, 20, 15)

        value_label = QLabel(value)
        value_label.setStyleSheet(f"""
            font-size: 28px; 
            font-weight: bold; 
            color: {color};
            qproperty-alignment: AlignCenter;
        """)

        title_label = QLabel(title)
        title_label.setStyleSheet("""
            font-size: 14px; 
            color: #666;
            qproperty-alignment: AlignCenter;
        """)

        layout.addWidget(value_label)
        layout.addWidget(title_label)

        return frame

    def _init_timers(self):
        """初始化定时器"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self._process_and_refresh_ui)
        self.update_timer.setInterval(300)  # 300ms批量刷新一次UI (平衡性能和响应性)
        self.update_timer.start()

    def _init_shortcuts(self):
        """初始化快捷键"""
        # F5: 强制同步所有状态
        self.sync_shortcut = QShortcut(QKeySequence("F5"), self)
        self.sync_shortcut.activated.connect(self.force_sync_all_status)

        # F6: 重启上传服务
        self.restart_shortcut = QShortcut(QKeySequence("F6"), self)
        self.restart_shortcut.activated.connect(self.restart_upload_service)

        # F7: 强制重新上传待上传记录
        self.reupload_shortcut = QShortcut(QKeySequence("F7"), self)
        self.reupload_shortcut.activated.connect(self.force_reupload_pending_records)

        # F8: 验证上传状态
        self.verify_shortcut = QShortcut(QKeySequence("F8"), self)
        self.verify_shortcut.activated.connect(self.verify_upload_status)

        # F9: 测试API连接
        self.api_test_shortcut = QShortcut(QKeySequence("F9"), self)
        self.api_test_shortcut.activated.connect(self._check_api_connectivity)

        # F10: 显示实时统计
        self.stats_shortcut = QShortcut(QKeySequence("F10"), self)
        self.stats_shortcut.activated.connect(self._show_current_stats)

        logging.info("💡 快捷键提示:")
        logging.info("   F5 - 强制同步所有记录状态")
        logging.info("   F6 - 重启上传服务")
        logging.info("   F7 - 强制重新上传待上传记录")
        logging.info("   F8 - 验证上传状态 (诊断是否真的未上传)")
        logging.info("   F9 - 测试API连接")
        logging.info("   F10 - 显示实时统计")

    def handle_data_update(self, records):
        """处理数据更新（将记录添加到缓冲区）"""
        if not records:
            return

        try:
            # 如果是单个记录，转换为列表
            if not isinstance(records, list):
                records = [records]

            # 非阻塞方式添加到缓冲区
            if self.buffer_mutex.tryLock():
                try:
                    for record in records:
                        # 限制缓冲区大小，防止内存溢出和UI阻塞
                        if len(self.update_buffer) > 200:  # 适当增加缓冲区大小
                            # 移除最旧的记录，保留最新的
                            removed_count = 0
                            while len(self.update_buffer) > 100:
                                if self.update_buffer:
                                    self.update_buffer.popleft()
                                    removed_count += 1
                                else:
                                    break
                            if removed_count > 0:
                                from datetime import datetime
                                self.buffer_stats['total_cleared'] += removed_count
                                self.buffer_stats['last_clear_time'] = datetime.now()
                                logging.info(f"UI缓冲区清理: 移除 {removed_count} 条旧记录，当前大小: {len(self.update_buffer)}，累计清理: {self.buffer_stats['total_cleared']}")
                        self.update_buffer.append(record)

                        # 更新缓冲区大小统计
                        current_size = len(self.update_buffer)
                        if current_size > self.buffer_stats['max_size']:
                            self.buffer_stats['max_size'] = current_size
                finally:
                    self.buffer_mutex.unlock()
            else:
                # 如果无法获取锁，直接丢弃数据，避免阻塞
                logging.warning("UI缓冲区忙，丢弃数据更新")

        except Exception as e:
            logging.error(f"处理数据更新异常: {str(e)}")

        # 不再立即调用UI更新，完全依赖定时器

    def _process_and_refresh_ui(self):
        """处理缓冲区记录并刷新UI统计信息 - 安全版本"""
        try:
            # 基本检查
            if not hasattr(self, 'update_buffer') or not hasattr(self, 'buffer_mutex') or not hasattr(self, 'tree'):
                return

            # 获取记录
            records = []
            if self.buffer_mutex.tryLock():
                try:
                    for _ in range(min(5, len(self.update_buffer))):
                        if self.update_buffer:
                            records.append(self.update_buffer.popleft())
                finally:
                    self.buffer_mutex.unlock()

            if not records:
                return
            self.tree.setUpdatesEnabled(False)
            new_item_added = False
            records_to_add_to_tree = []
            processed_count = 0

            # 添加处理时间限制，避免UI长时间阻塞
            import time
            start_time = time.time()
            max_process_time = 0.1  # 最多处理100ms，提高处理效率

            for record in records:
                try:
                    # 检查处理时间，避免UI长时间阻塞
                    if time.time() - start_time > max_process_time:
                        logging.debug(f"UI处理超时，已处理 {processed_count} 条记录")
                        break

                    if processed_count >= 10:
                        # 不再放回缓冲区，直接丢弃，避免死锁
                        break

                    # 验证记录的有效性
                    if not hasattr(record, 'chip_id') or not hasattr(record, 'scan_time'):
                        logging.warning(f"无效的记录对象: {record}")
                        continue

                    key = f"{record.chip_id}-{record.scan_time}"
                    status = getattr(record, 'status', '待上传')  # 默认状态

                    # 调试：记录键值生成
                    if processed_count < 3:  # 只记录前3个
                        logging.debug(f"🔑 生成界面键值: {key}")

                    if key not in self.data_items:
                        item = QTreeWidgetItem()
                        item.setText(0, str(record.chip_id))
                        item.setText(1, str(getattr(record, 'device_id', '')))
                        item.setText(2, str(record.scan_time))
                        item.setText(3, self._status_text(status))
                        for col in range(4):  # 包括状态列
                            item.setTextAlignment(col, Qt.AlignCenter)
                        records_to_add_to_tree.append(item)
                        self.data_items[key] = item
                        new_item_added = True
                    else:
                        # 更新现有记录的状态
                        item = self.data_items[key]
                        item.setText(3, self._status_text(status))
                    processed_count += 1

                except Exception as e:
                    logging.error(f"处理单个记录异常: {str(e)}, 记录: {record}")
                    continue
            if records_to_add_to_tree:
                self.tree.addTopLevelItems(records_to_add_to_tree)

                # 限制显示的记录数量，自动清理旧记录
                if self.tree.topLevelItemCount() > MAX_UI_RECORDS:
                    items_to_remove = self.tree.topLevelItemCount() - MAX_UI_RECORDS
                    removed_keys = []

                    for _ in range(items_to_remove):
                        item = self.tree.takeTopLevelItem(0)  # 移除最旧的记录
                        if item:
                            # 从data_items中也移除，并记录被移除的键
                            for key, stored_item in list(self.data_items.items()):
                                if stored_item == item:
                                    removed_keys.append(key)
                                    del self.data_items[key]
                                    break

                    if removed_keys:
                        logging.info(f"🗑️ 自动清理了 {len(removed_keys)} 条旧记录")
                        logging.debug(f"被清理的记录键: {removed_keys[:5]}...")  # 只显示前5个

            if new_item_added:
                self.tree.scrollToBottom()
            self.tree.setUpdatesEnabled(True)
        except Exception as e:
            import traceback
            logging.error(f"❌ UI更新异常: {str(e)}")
            logging.error(f"📍 错误位置: _process_and_refresh_ui 方法")
            logging.error(f"🔍 详细错误信息: {traceback.format_exc()}")

            # 确保UI状态正确
            try:
                if hasattr(self, 'tree') and self.tree:
                    self.tree.setUpdatesEnabled(True)
                    logging.info("✅ UI状态已恢复")
            except Exception as recovery_error:
                logging.error(f"❌ UI状态恢复失败: {str(recovery_error)}")
                pass

        # 简化统计信息更新，避免跨线程锁竞争
        try:
            if self.server_handler:
                # 使用非阻塞方式获取统计信息
                try:
                    current_total_scans = len(self.server_handler.data_records)
                    if current_total_scans != self.last_total_scans:
                        self.scan_count.layout().itemAt(0).widget().setText(str(current_total_scans))
                        self.last_total_scans = current_total_scans
                except:
                    pass  # 忽略统计错误，避免界面死锁

                try:
                    # 使用非阻塞锁获取芯片统计
                    if self.server_handler.chip_mutex.tryLock():
                        try:
                            current_unique_chips = len(self.server_handler.unique_chips)
                            if current_unique_chips != self.last_unique_chips:
                                self.participant_count.layout().itemAt(0).widget().setText(str(current_unique_chips))
                                self.last_unique_chips = current_unique_chips
                        finally:
                            self.server_handler.chip_mutex.unlock()
                except:
                    pass  # 忽略统计错误
                current_device_text = self.device_list.text()
                if current_device_text != self.last_device_list_text:
                    device_count_val = 0
                    if current_device_text != "无活动连接" and current_device_text.strip():
                        device_count_val = len(current_device_text.split("\n"))
                    self.device_count.layout().itemAt(0).widget().setText(str(device_count_val))
                    self.last_device_list_text = current_device_text
        except Exception as e:
            logging.error(f"UI statistics refresh failed: {str(e)}")
            pass # Avoid crashing UI on stats refresh error

    def _status_text(self, status):
        """状态显示文本"""
        status_map = {
            "待上传": ("待上传", "#FF9800"),
            "上传中": ("上传中...", "#2196F3"),
            "已成功": ("✓ 成功", "#4CAF50"),
            "已失败": ("✗ 失败", "#F44336")
        }
        text, _ = status_map.get(status, ("未知", "#9E9E9E"))
        return text

    def handle_connection_msg(self, msg):
        """处理设备连接消息"""
        try:
            logging.info(f"收到连接消息: {msg}")  # 添加调试日志

            current_devices = self.device_list.text().split("\n")
            if "无活动连接" in current_devices:
                current_devices = []

            # 处理连接消息
            if "已连接:" in msg:
                device = msg.split(":", 1)[1].strip()
                if device not in current_devices:
                    current_devices.append(device)
                    logging.info(f"设备上线: {device}")
            elif "已断开:" in msg:
                device = msg.split(":", 1)[1].strip()
                if device in current_devices:
                    current_devices.remove(device)
                    logging.info(f"设备下线: {device}")
            # 兼容旧格式
            elif "读卡器地址:" in msg:
                device = msg.split(":", 1)[1].strip()
                if device not in current_devices:
                    current_devices.append(device)
                    logging.info(f"设备上线(旧格式): {device}")
            elif "读卡器断开:" in msg:
                device = msg.split(":", 1)[1].strip()
                if device in current_devices:
                    current_devices.remove(device)
                    logging.info(f"设备下线(旧格式): {device}")
            else:
                logging.warning(f"未识别的连接消息格式: {msg}")

            # 更新设备列表显示
            device_text = "\n".join(current_devices) if current_devices else "无活动连接"
            self.device_list.setText(device_text)
            logging.info(f"设备列表已更新: {device_text}")

            # 立即更新设备数量统计
            device_count = len(current_devices) if current_devices else 0
            self.device_count.layout().itemAt(0).widget().setText(str(device_count))

        except Exception as e:
            logging.error(f"连接消息处理失败: {str(e)}")

    def handle_status_update(self, records):
        """处理批量上传状态更新（减少UI阻塞）"""
        try:
            if not records:
                logging.debug("📭 收到空的状态更新")
                return

            logging.info(f"📥 收到状态更新信号: {len(records)} 条记录")

            # 统计状态类型
            status_counts = {}
            for record in records:
                status = getattr(record, 'status', '未知')
                status_counts[status] = status_counts.get(status, 0) + 1
            logging.info(f"📊 收到的状态分布: {status_counts}")

            # 调试：显示前几个记录的键值格式
            if len(records) > 0:
                sample_record = records[0]
                sample_key = f"{sample_record.chip_id}-{sample_record.scan_time}"
                logging.info(f"🔍 状态更新键值示例: {sample_key}")

                # 显示界面中的前几个键值
                ui_keys = list(self.data_items.keys())[:3]
                logging.info(f"🔍 界面键值示例: {ui_keys}")

            # 批量处理状态更新，减少UI重绘次数
            self.tree.setUpdatesEnabled(False)
            updated_count = 0
            found_count = 0
            not_found_count = 0

            try:
                for record in records:
                    # 尝试多种键值匹配方式
                    primary_key = f"{record.chip_id}-{record.scan_time}"
                    found_item = None
                    match_method = ""

                    # 方法1：精确匹配
                    if primary_key in self.data_items:
                        found_item = self.data_items[primary_key]
                        match_method = "精确匹配"
                    else:
                        # 方法2：通过芯片ID模糊匹配
                        for existing_key, existing_item in self.data_items.items():
                            if existing_key.startswith(f"{record.chip_id}-"):
                                found_item = existing_item
                                match_method = "芯片ID匹配"
                                break

                        # 方法3：直接在树形控件中查找
                        if not found_item:
                            for i in range(self.tree.topLevelItemCount()):
                                item = self.tree.topLevelItem(i)
                                if item and item.text(0) == record.chip_id:
                                    # 找到相同芯片ID的项目
                                    found_item = item
                                    match_method = "树形控件匹配"
                                    break

                    # 更新找到的项目
                    if found_item:
                        found_count += 1
                        old_status = found_item.text(3)
                        new_status = record.status

                        # 只有状态真正改变时才更新
                        if old_status != new_status:
                            found_item.setText(3, new_status)
                            updated_count += 1
                            logging.debug(f"🔄 状态更新({match_method}): {record.chip_id} {old_status} → {new_status}")

                            # 根据状态设置不同的字体颜色
                            if new_status == "已成功":
                                found_item.setForeground(3, QColor(0, 128, 0))      # 绿色字体
                            elif new_status == "已失败":
                                found_item.setForeground(3, QColor(255, 0, 0))      # 红色字体
                            elif new_status == "重试失败":
                                found_item.setForeground(3, QColor(128, 0, 128))    # 紫色字体
                            else:
                                found_item.setForeground(3, QColor(0, 0, 0))        # 黑色字体（默认）
                        else:
                            logging.debug(f"⏭️  状态无变化({match_method}): {record.chip_id} 保持 {old_status}")
                    else:
                        not_found_count += 1
                        logging.debug(f"❓ 完全未找到界面项目: {primary_key} (芯片ID: {record.chip_id})")

                logging.info(f"✅ UI状态更新完成:")
                logging.info(f"   找到项目: {found_count}")
                logging.info(f"   未找到项目: {not_found_count}")
                logging.info(f"   实际更新: {updated_count}")

                # 如果未找到项目较多，给出提示
                if not_found_count > 0:
                    total_ui_items = len(self.data_items)
                    logging.info(f"📊 当前界面记录总数: {total_ui_items}")
                    if not_found_count > len(records) * 0.1:  # 超过10%未找到
                        logging.warning(f"⚠️ 未找到项目比例较高: {not_found_count}/{len(records)} ({not_found_count/len(records)*100:.1f}%)")
                        logging.info("💡 这可能是因为界面记录被自动清理，或者时间格式不匹配")

            finally:
                self.tree.setUpdatesEnabled(True)

        except Exception as e:
            logging.error(f"❌ 批量状态更新处理失败: {str(e)}")
            import traceback
            logging.error(f"详细错误: {traceback.format_exc()}")
            # 确保UI状态正确
            try:
                self.tree.setUpdatesEnabled(True)
            except:
                pass

    def toggle_server(self):
        """切换服务器状态"""
        if self.server_handler and self.server_handler.running:
            self._stop_server()
        else:
            self._start_server()

    def _start_server(self):
        """启动服务器"""
        try:
            self.server_handler = TcpServerHandler(self)
            self.server_handler.update_signal.connect(self.handle_data_update, Qt.QueuedConnection)
            self.server_handler.connection_update_signal.connect(self.handle_connection_msg, Qt.QueuedConnection)

            # 启动服务器（这会创建上传管理器）
            self.server_handler.start()

            # 连接上传管理器的状态更新信号（在启动后连接）
            if hasattr(self.server_handler, 'upload_manager') and self.server_handler.upload_manager:
                self.server_handler.upload_manager.status_update_signal.connect(self.handle_status_update, Qt.QueuedConnection)
            self.server_status.setText("🟢 服务运行中")
            self.start_btn.setText("停止服务")
            self.start_btn.setStyleSheet("background: #f44336; color: white;")
            self.tree.clear()
            self.data_items.clear()
            self.last_total_scans = 0
            self.last_unique_chips = 0
            self.last_device_list_text = ""
            self.device_list.setText("无活动连接")
            # Manually trigger a refresh to show 0 counts initially for stats
            self._process_and_refresh_ui() 
            logging.info("服务器启动成功")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"服务启动失败: {str(e)}")

    def _stop_server(self):
        """停止服务器 - 使用后台线程避免UI阻塞"""
        if not self.server_handler:
            return

        # 立即更新UI状态，给用户反馈
        self.start_btn.setText("停止中...")
        self.start_btn.setEnabled(False)
        self.server_status.setText("🟡 正在停止...")

        # 创建并启动后台停止线程
        self.stop_thread = ServerStopThread(self.server_handler, self)
        self.stop_thread.finished_signal.connect(self._on_stop_finished)
        self.stop_thread.progress_signal.connect(self._on_stop_progress)
        self.stop_thread.start()

        logging.info("🛑 开始后台停止服务...")

    def _on_stop_progress(self, message):
        """处理停止进度更新"""
        self.server_status.setText(f"🟡 {message}")
        logging.info(message)

    def _on_stop_finished(self, result):
        """处理停止完成"""
        try:
            if 'error' in result:
                logging.error(f"停止服务出错: {result['error']}")
                self.server_status.setText("🔴 停止出错")
            else:
                # 显示统计结果
                before = result['before']
                after = result['after']

                logging.info("=" * 60)
                logging.info("📊 停止后状态统计:")
                logging.info(f"   待上传: {before['pending']} → {after['pending']} (变化: {after['pending'] - before['pending']})")
                logging.info(f"   已成功: {before['success']} → {after['success']} (变化: {after['success'] - before['success']})")
                logging.info(f"   已失败: {before['failed']} → {after['failed']} (变化: {after['failed'] - before['failed']})")

                if after['pending'] > 0:
                    logging.warning(f"⚠️  仍有 {after['pending']} 条记录未上传!")
                    logging.info("🔄 自动启动智能处理流程...")

                    # 自动处理流程
                    self._auto_handle_pending_records(after['pending'])
                else:
                    logging.info("✅ 所有记录都已处理完成")
                logging.info("=" * 60)

            # 更新UI状态
            self.server_status.setText("🔴 服务未运行")
            self.start_btn.setText("启动服务")
            self.start_btn.setStyleSheet("background: #4CAF50; color: white;")
            self.start_btn.setEnabled(True)
            self.device_list.setText("无活动连接")

            # 清理线程引用
            self.stop_thread = None

            logging.info("🛑 服务器已停止")

        except Exception as e:
            logging.error(f"处理停止完成事件异常: {str(e)}")
            # 确保UI状态正确
            self.start_btn.setText("启动服务")
            self.start_btn.setEnabled(True)
            self.server_status.setText("🔴 服务未运行")

    def _fix_pending_status(self):
        """智能修复可能的状态同步问题"""
        try:
            fixed_count = 0
            skipped_count = 0
            total_items = self.tree.topLevelItemCount()

            logging.info("🔧 开始智能状态分析...")

            for i in range(total_items):
                item = self.tree.topLevelItem(i)
                if item and item.text(3) == "待上传":
                    chip_id = item.text(0)
                    scan_time = item.text(2)

                    # 多重判断逻辑
                    should_fix, reason = self._should_fix_pending_record(chip_id, scan_time)

                    if should_fix:
                        try:
                            item.setText(3, "已成功")
                            item.setForeground(3, QColor(0, 128, 0))  # 绿色字体
                            fixed_count += 1
                            logging.debug(f"🔧 修复记录: {chip_id} - {reason}")
                        except Exception as e:
                            logging.error(f"修复单个记录状态失败: {str(e)}")
                    else:
                        skipped_count += 1
                        logging.debug(f"⏭️ 保持待上传: {chip_id} - {reason}")

            # 详细的修复报告
            if fixed_count > 0:
                logging.info(f"✅ 智能状态修复完成:")
                logging.info(f"   修复记录: {fixed_count} 条")
                logging.info(f"   保持待上传: {skipped_count} 条")
                logging.info("💡 修复的记录基于多重判断逻辑，很可能已经成功上传")
            else:
                if skipped_count > 0:
                    logging.warning(f"⚠️ 有 {skipped_count} 条记录确实可能未上传")
                else:
                    logging.info("ℹ️ 没有待上传记录需要处理")

        except Exception as e:
            logging.error(f"状态修复异常: {str(e)}")

    def _should_fix_pending_record(self, chip_id, scan_time):
        """判断是否应该修复待上传记录的状态"""
        try:
            from datetime import datetime

            # 判断1: 检查扫描时间格式和有效性
            if not scan_time or len(scan_time) < 10:
                return False, "扫描时间无效"

            # 判断2: 解析扫描时间
            try:
                # 尝试多种时间格式
                time_formats = [
                    "%Y-%m-%d %H:%M:%S",
                    "%Y-%m-%d %H:%M:%S.%f",
                    "%Y/%m/%d %H:%M:%S",
                    "%m/%d/%Y %H:%M:%S"
                ]

                scan_datetime = None
                for fmt in time_formats:
                    try:
                        # 处理可能的毫秒部分
                        time_str = scan_time
                        if '.' in time_str and len(time_str.split('.')[-1]) > 6:
                            # 截断过长的毫秒部分
                            time_str = time_str[:time_str.rfind('.') + 7]

                        scan_datetime = datetime.strptime(time_str, fmt)
                        break
                    except ValueError:
                        continue

                if not scan_datetime:
                    return False, "时间格式无法解析"

            except Exception:
                return False, "时间解析异常"

            # 判断3: 检查记录年龄
            now = datetime.now()
            age_seconds = (now - scan_datetime).total_seconds()

            # 如果记录超过30秒，很可能已经被处理
            if age_seconds > 30:
                return True, f"记录较旧({age_seconds:.1f}秒)，很可能已上传"

            # 判断4: 检查是否在服务停止状态
            # 如果服务已停止且记录超过10秒，也很可能已处理
            if not self.server_handler and age_seconds > 10:
                return True, f"服务已停止且记录较旧({age_seconds:.1f}秒)"

            # 判断5: 检查芯片ID格式
            # 有效的芯片ID格式通常表示这是真实数据
            if chip_id and len(chip_id) >= 6 and chip_id.isalnum():
                if age_seconds > 15:  # 有效芯片ID的记录，15秒后很可能已处理
                    return True, f"有效芯片ID且记录较旧({age_seconds:.1f}秒)"

            # 判断6: 检查上传队列状态
            if self.server_handler and hasattr(self.server_handler, 'upload_manager'):
                upload_manager = self.server_handler.upload_manager
                if upload_manager:
                    # 检查上传队列中是否还有这个记录
                    record_in_queue = self._check_record_in_upload_queue(chip_id, upload_manager)
                    if not record_in_queue and age_seconds > 5:
                        return True, f"不在上传队列且记录较旧({age_seconds:.1f}秒)"

            # 默认保持待上传状态
            return False, f"记录较新({age_seconds:.1f}秒)，可能确实未上传"

        except Exception as e:
            logging.error(f"判断记录状态异常: {str(e)}")
            return False, "判断异常"

    def _check_record_in_upload_queue(self, chip_id, upload_manager):
        """检查记录是否还在上传队列中"""
        try:
            # 检查上传队列
            with upload_manager.upload_mutex.tryLock():
                if upload_manager.upload_mutex.tryLock():
                    try:
                        for record in upload_manager.upload_queue:
                            if hasattr(record, 'chip_id') and record.chip_id == chip_id:
                                return True
                    finally:
                        upload_manager.upload_mutex.unlock()

            # 检查重试队列
            with upload_manager.retry_mutex.tryLock():
                if upload_manager.retry_mutex.tryLock():
                    try:
                        for record in upload_manager.retry_queue:
                            if hasattr(record, 'chip_id') and record.chip_id == chip_id:
                                return True
                    finally:
                        upload_manager.retry_mutex.unlock()

            return False
        except Exception as e:
            logging.debug(f"检查上传队列异常: {str(e)}")
            return False  # 异常时保守处理

    def force_sync_all_status(self):
        """强制同步所有记录状态 - 手动调用"""
        try:
            logging.info("🔄 开始强制同步所有状态...")

            # 1. 强制处理所有缓冲区数据
            self._process_and_refresh_ui()

            # 2. 如果有上传管理器，强制发送状态更新
            if self.server_handler and hasattr(self.server_handler, 'upload_manager'):
                upload_manager = self.server_handler.upload_manager
                if upload_manager:
                    # 强制发送所有状态更新
                    upload_manager._flush_status_updates(force=True)
                    logging.info("✅ 已强制发送状态更新")

                    # 显示队列状态
                    self._log_upload_queue_status(upload_manager)

            # 3. 等待状态更新到达
            import time
            time.sleep(1)

            # 4. 再次处理UI更新
            self._process_and_refresh_ui()

            # 5. 统计当前状态
            total_items = self.tree.topLevelItemCount()
            pending_count = 0
            success_count = 0
            failed_count = 0

            for i in range(total_items):
                item = self.tree.topLevelItem(i)
                if item:
                    status = item.text(3)
                    if status == "待上传":
                        pending_count += 1
                    elif status == "已成功":
                        success_count += 1
                    elif status == "已失败":
                        failed_count += 1

            logging.info("📊 强制同步后状态统计:")
            logging.info(f"   总记录数: {total_items}")
            logging.info(f"   待上传: {pending_count}")
            logging.info(f"   已成功: {success_count}")
            logging.info(f"   已失败: {failed_count}")

            # 6. 智能处理剩余的待上传记录
            if pending_count > 0:
                logging.warning(f"⚠️ 仍有 {pending_count} 条记录待上传")
                logging.info("🔍 开始智能分析这些记录...")
                self._fix_pending_status()

                # 重新统计修复后的状态
                final_pending = 0
                for i in range(total_items):
                    item = self.tree.topLevelItem(i)
                    if item and item.text(3) == "待上传":
                        final_pending += 1

                if final_pending < pending_count:
                    logging.info(f"✅ 修复了 {pending_count - final_pending} 条记录")
                if final_pending > 0:
                    logging.warning(f"❗ 最终仍有 {final_pending} 条记录可能确实未上传")
                    logging.info("🔧 开始诊断未上传原因...")
                    self._diagnose_upload_failures(final_pending)
            else:
                logging.info("✅ 所有记录状态已同步")

        except Exception as e:
            logging.error(f"强制同步状态异常: {str(e)}")

    def _log_upload_queue_status(self, upload_manager):
        """记录上传队列状态"""
        try:
            upload_size = 0
            retry_size = 0

            if upload_manager.upload_mutex.tryLock():
                try:
                    upload_size = len(upload_manager.upload_queue)
                finally:
                    upload_manager.upload_mutex.unlock()

            if upload_manager.retry_mutex.tryLock():
                try:
                    retry_size = len(upload_manager.retry_queue)
                finally:
                    upload_manager.retry_mutex.unlock()

            logging.info(f"📋 上传队列状态: 待上传 {upload_size} 条, 重试 {retry_size} 条")

            if upload_size > 0 or retry_size > 0:
                logging.info("💡 队列中仍有数据，这些界面记录可能确实未上传")
            else:
                logging.info("💡 队列已空，界面的'待上传'记录很可能是状态更新延迟")

        except Exception as e:
            logging.debug(f"获取队列状态异常: {str(e)}")

    def _diagnose_upload_failures(self, pending_count):
        """诊断上传失败的原因"""
        try:
            logging.info("🔍 开始详细诊断...")

            # 1. 检查API连接状态
            self._check_api_connectivity()

            # 2. 分析未上传记录的特征
            self._analyze_pending_records()

            # 3. 检查上传管理器状态
            self._check_upload_manager_status()

            # 4. 提供解决建议
            self._provide_solutions(pending_count)

        except Exception as e:
            logging.error(f"诊断异常: {str(e)}")

    def _check_api_connectivity(self):
        """检查API连接状态"""
        try:
            logging.info("🌐 检查API连接...")

            # 尝试多种方式导入和测试
            requests_available = False

            # 方式1: 直接导入
            try:
                import requests
                requests_available = True
                logging.debug("✅ requests模块导入成功")
            except ImportError as e:
                logging.warning(f"⚠️ 无法导入requests模块: {str(e)}")

            # 方式2: 如果requests可用，测试API连接
            if requests_available:
                try:
                    # 从config获取API URL
                    from config import API_URL

                    # 简单的连接测试
                    logging.info(f"🔗 测试连接到: {API_URL}")
                    response = requests.get(API_URL, timeout=5)

                    if response.status_code == 200:
                        logging.info("✅ API服务器连接正常")
                        logging.info(f"📊 响应状态: {response.status_code}")
                    elif response.status_code == 404:
                        logging.info("✅ API服务器可达 (404是正常的，因为我们没有发送正确的数据)")
                    else:
                        logging.warning(f"⚠️ API服务器响应: {response.status_code}")

                except requests.exceptions.Timeout:
                    logging.error("❌ API服务器连接超时 (5秒)")
                except requests.exceptions.ConnectionError:
                    logging.error("❌ 无法连接到API服务器")
                    logging.info("💡 请检查:")
                    logging.info("   - 网络连接是否正常")
                    logging.info("   - API服务器是否运行")
                    logging.info("   - 防火墙设置")
                except Exception as e:
                    logging.error(f"❌ API连接测试失败: {str(e)}")
            else:
                # 方式3: 使用系统的网络测试
                logging.info("🔄 使用系统网络测试...")
                self._test_network_connectivity()

        except Exception as e:
            logging.error(f"API连接检查异常: {str(e)}")

    def _test_network_connectivity(self):
        """使用系统命令测试网络连接"""
        try:
            import subprocess
            import sys

            # 获取API URL的主机部分
            try:
                from config import API_URL
                from urllib.parse import urlparse
                parsed_url = urlparse(API_URL)
                host = parsed_url.netloc.split(':')[0]  # 去掉端口号

                logging.info(f"🌐 测试网络连接到: {host}")

                # Windows系统使用ping命令
                if sys.platform.startswith('win'):
                    result = subprocess.run(['ping', '-n', '1', host],
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        logging.info("✅ 网络连接正常 (ping成功)")
                    else:
                        logging.warning("⚠️ 网络连接可能有问题 (ping失败)")
                else:
                    # Linux/Mac系统
                    result = subprocess.run(['ping', '-c', '1', host],
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        logging.info("✅ 网络连接正常 (ping成功)")
                    else:
                        logging.warning("⚠️ 网络连接可能有问题 (ping失败)")

            except Exception as e:
                logging.debug(f"网络测试异常: {str(e)}")
                logging.info("💡 无法进行网络测试，请手动检查网络连接")

        except Exception as e:
            logging.debug(f"系统网络测试异常: {str(e)}")

    def test_api_with_real_data(self):
        """使用真实数据测试API连接"""
        try:
            logging.info("🧪 使用真实数据测试API...")

            # 检查是否有上传管理器
            if not self.server_handler or not hasattr(self.server_handler, 'upload_manager'):
                logging.warning("⚠️ 上传管理器不可用，无法测试API")
                return

            upload_manager = self.server_handler.upload_manager
            if not upload_manager:
                logging.warning("⚠️ 上传管理器未初始化")
                return

            # 创建一个测试记录
            class TestRecord:
                def __init__(self):
                    self.chip_id = "TEST001"
                    self.device_id = "测试设备"
                    self.scan_time = "2025-07-13 16:00:00"
                    self.status = "待上传"

            test_record = TestRecord()
            logging.info(f"📤 发送测试记录: {test_record.chip_id}")

            # 使用API客户端发送测试数据
            success = upload_manager.api_client.send_data(test_record)

            if success:
                logging.info("✅ API测试成功！服务器响应正常")
            else:
                logging.error("❌ API测试失败！请检查服务器状态")

        except Exception as e:
            logging.error(f"API测试异常: {str(e)}")
            import traceback
            logging.debug(f"详细错误: {traceback.format_exc()}")

    def _show_current_stats(self):
        """显示当前实时统计信息"""
        try:
            logging.info("📊 ===== 实时统计信息 =====")

            # 1. 界面统计
            total_items = self.tree.topLevelItemCount()
            ui_pending = 0
            ui_success = 0
            ui_failed = 0

            for i in range(total_items):
                item = self.tree.topLevelItem(i)
                if item:
                    status = item.text(3)
                    if status == "待上传":
                        ui_pending += 1
                    elif status == "已成功":
                        ui_success += 1
                    elif status == "已失败":
                        ui_failed += 1

            logging.info(f"🖥️ 界面统计:")
            logging.info(f"   总记录: {total_items}")
            logging.info(f"   待上传: {ui_pending}")
            logging.info(f"   已成功: {ui_success}")
            logging.info(f"   已失败: {ui_failed}")

            # 2. 服务器统计
            if self.server_handler:
                logging.info("🔄 强制触发服务器统计...")
                if hasattr(self.server_handler, '_log_statistics'):
                    self.server_handler._log_statistics()
                else:
                    logging.warning("⚠️ 服务器统计方法不可用")
            else:
                logging.warning("⚠️ 服务器未运行")

            # 3. 上传管理器详细统计
            if self.server_handler and hasattr(self.server_handler, 'upload_manager'):
                upload_manager = self.server_handler.upload_manager
                if upload_manager:
                    self._show_upload_manager_details(upload_manager)

            logging.info("📊 ========================")

        except Exception as e:
            logging.error(f"显示统计信息异常: {str(e)}")

    def _show_upload_manager_details(self, upload_manager):
        """显示上传管理器详细信息"""
        try:
            logging.info("📤 上传管理器详情:")

            # 运行状态
            running = getattr(upload_manager, 'running', False)
            logging.info(f"   运行状态: {'🟢 运行中' if running else '🔴 已停止'}")

            # 队列详情
            upload_size = 0
            retry_size = 0

            if hasattr(upload_manager, 'upload_mutex') and hasattr(upload_manager, 'upload_queue'):
                if upload_manager.upload_mutex.tryLock():
                    try:
                        upload_size = len(upload_manager.upload_queue)
                        if upload_size > 0:
                            # 显示队列中的前几个记录
                            sample_chips = []
                            for i, record in enumerate(upload_manager.upload_queue):
                                if i < 3:
                                    sample_chips.append(getattr(record, 'chip_id', 'Unknown'))
                            logging.info(f"   上传队列: {upload_size} 条 {sample_chips}{'...' if upload_size > 3 else ''}")
                        else:
                            logging.info(f"   上传队列: {upload_size} 条 (空)")
                    finally:
                        upload_manager.upload_mutex.unlock()

            if hasattr(upload_manager, 'retry_mutex') and hasattr(upload_manager, 'retry_queue'):
                if upload_manager.retry_mutex.tryLock():
                    try:
                        retry_size = len(upload_manager.retry_queue)
                        if retry_size > 0:
                            sample_chips = []
                            for i, record in enumerate(upload_manager.retry_queue):
                                if i < 3:
                                    sample_chips.append(getattr(record, 'chip_id', 'Unknown'))
                            logging.info(f"   重试队列: {retry_size} 条 {sample_chips}{'...' if retry_size > 3 else ''}")
                        else:
                            logging.info(f"   重试队列: {retry_size} 条 (空)")
                    finally:
                        upload_manager.retry_mutex.unlock()

            # 线程池状态
            if hasattr(upload_manager, 'api_executor'):
                try:
                    # 简单测试线程池是否工作
                    def test_task():
                        return True
                    future = upload_manager.api_executor.submit(test_task)
                    future.result(timeout=1)  # 等待结果但不使用
                    logging.info("   线程池: 🟢 正常工作")
                except Exception as e:
                    logging.info(f"   线程池: 🔴 异常 ({str(e)})")

            # 定时器状态
            timer_status = []
            if hasattr(upload_manager, 'batch_timer'):
                timer_status.append(f"批量: {'🟢' if upload_manager.batch_timer.isActive() else '🔴'}")
            if hasattr(upload_manager, 'retry_timer'):
                timer_status.append(f"重试: {'🟢' if upload_manager.retry_timer.isActive() else '🔴'}")
            if hasattr(upload_manager, 'status_update_timer'):
                timer_status.append(f"状态: {'🟢' if upload_manager.status_update_timer.isActive() else '🔴'}")

            if timer_status:
                logging.info(f"   定时器: {' | '.join(timer_status)}")

        except Exception as e:
            logging.error(f"显示上传管理器详情异常: {str(e)}")

    def _analyze_pending_records(self):
        """分析未上传记录的特征"""
        try:
            logging.info("📊 分析未上传记录特征...")

            pending_records = []
            total_items = self.tree.topLevelItemCount()

            for i in range(total_items):
                item = self.tree.topLevelItem(i)
                if item and item.text(3) == "待上传":
                    chip_id = item.text(0)
                    device_id = item.text(1)
                    scan_time = item.text(2)

                    pending_records.append({
                        'chip_id': chip_id,
                        'device_id': device_id,
                        'scan_time': scan_time
                    })

            if pending_records:
                # 分析设备分布
                device_counts = {}
                for record in pending_records:
                    device = record['device_id']
                    device_counts[device] = device_counts.get(device, 0) + 1

                logging.info(f"📱 设备分布: {device_counts}")

                # 分析时间分布
                from datetime import datetime
                recent_count = 0
                old_count = 0

                for record in pending_records:
                    try:
                        scan_time = record['scan_time']
                        if len(scan_time) >= 19:
                            scan_dt = datetime.strptime(scan_time[:19], "%Y-%m-%d %H:%M:%S")
                            age = (datetime.now() - scan_dt).total_seconds()
                            if age < 60:  # 1分钟内
                                recent_count += 1
                            else:
                                old_count += 1
                    except:
                        pass

                logging.info(f"⏰ 时间分布: 最近1分钟 {recent_count} 条, 较早 {old_count} 条")

                # 显示前几个示例
                sample_count = min(3, len(pending_records))
                logging.info(f"📝 示例记录 (前{sample_count}条):")
                for i in range(sample_count):
                    record = pending_records[i]
                    logging.info(f"   {i+1}. 芯片:{record['chip_id']}, 设备:{record['device_id']}, 时间:{record['scan_time']}")

        except Exception as e:
            logging.error(f"分析记录特征异常: {str(e)}")

    def _check_upload_manager_status(self):
        """检查上传管理器状态"""
        try:
            if not self.server_handler or not hasattr(self.server_handler, 'upload_manager'):
                logging.warning("⚠️ 上传管理器不可用")
                return

            upload_manager = self.server_handler.upload_manager
            if not upload_manager:
                logging.warning("⚠️ 上传管理器未初始化")
                return

            logging.info("🔧 检查上传管理器状态...")

            # 检查运行状态
            if hasattr(upload_manager, 'running'):
                logging.info(f"🏃 运行状态: {upload_manager.running}")

            # 检查线程池状态
            if hasattr(upload_manager, 'api_executor'):
                try:
                    # 尝试提交一个测试任务
                    def test_task():
                        return "test"

                    future = upload_manager.api_executor.submit(test_task)
                    future.result(timeout=1)  # 等待结果但不使用
                    logging.info("✅ 线程池工作正常")
                except Exception as e:
                    logging.error(f"❌ 线程池异常: {str(e)}")

        except Exception as e:
            logging.error(f"检查上传管理器异常: {str(e)}")

    def _provide_solutions(self, pending_count):
        """提供解决方案建议"""
        try:
            logging.info("💡 解决方案建议:")

            if pending_count > 50:
                logging.info("1. 📊 记录数量较多，建议:")
                logging.info("   - 检查网络连接是否稳定")
                logging.info("   - 检查API服务器负载")
                logging.info("   - 考虑分批重新上传")

            logging.info("2. 🔄 立即解决方案:")
            logging.info("   - 按 F6 键重新启动上传服务")
            logging.info("   - 按 F7 键强制重新上传所有待上传记录")
            logging.info("   - 按 F8 键验证上传状态")
            logging.info("   - 按 F9 键测试API连接")

            logging.info("3. 🛠️ 手动操作:")
            logging.info("   - 重启程序可能解决线程池问题")
            logging.info("   - 检查API服务器是否正常运行")
            logging.info("   - 确认网络防火墙设置")

        except Exception as e:
            logging.error(f"提供解决方案异常: {str(e)}")

    def force_reupload_pending_records(self):
        """强制重新上传所有待上传记录"""
        try:
            logging.info("🔄 开始强制重新上传待上传记录...")

            if not self.server_handler or not hasattr(self.server_handler, 'upload_manager'):
                logging.error("❌ 上传管理器不可用")
                return

            upload_manager = self.server_handler.upload_manager
            if not upload_manager:
                logging.error("❌ 上传管理器未初始化")
                return

            # 收集所有待上传记录
            pending_records = []
            total_items = self.tree.topLevelItemCount()

            for i in range(total_items):
                item = self.tree.topLevelItem(i)
                if item and item.text(3) == "待上传":
                    chip_id = item.text(0)
                    device_id = item.text(1)
                    scan_time = item.text(2)

                    # 创建记录对象
                    class ReuploadRecord:
                        def __init__(self, chip_id, device_id, scan_time):
                            self.chip_id = chip_id
                            self.device_id = device_id
                            self.scan_time = scan_time
                            self.status = "待上传"

                    record = ReuploadRecord(chip_id, device_id, scan_time)
                    pending_records.append(record)

            if not pending_records:
                logging.info("ℹ️ 没有待上传记录需要重新上传")
                return

            logging.info(f"📤 准备重新上传 {len(pending_records)} 条记录")

            # 分批重新上传
            batch_size = 10  # 小批量，避免超时
            for i in range(0, len(pending_records), batch_size):
                batch = pending_records[i:i + batch_size]
                logging.info(f"📦 重新上传第 {i//batch_size + 1} 批: {len(batch)} 条记录")

                # 添加到上传队列
                for record in batch:
                    upload_manager.add_record(record)

                # 等待一下让这批处理
                import time
                time.sleep(2)

            logging.info("✅ 所有记录已重新加入上传队列")
            logging.info("💡 请等待几分钟观察上传结果")

        except Exception as e:
            logging.error(f"强制重新上传异常: {str(e)}")

    def restart_upload_service(self):
        """重启上传服务"""
        try:
            logging.info("🔄 重启上传服务...")

            if not self.server_handler:
                logging.error("❌ 服务器处理器不可用")
                return

            # 重新初始化上传管理器
            if hasattr(self.server_handler, 'upload_manager'):
                old_manager = self.server_handler.upload_manager
                if old_manager:
                    logging.info("🛑 停止旧的上传管理器...")
                    old_manager.stop()

            # 创建新的上传管理器
            logging.info("🚀 启动新的上传管理器...")
            from upload_manager import UploadManager
            new_manager = UploadManager()
            new_manager.status_update_signal.connect(self.handle_status_update)
            new_manager.start()

            self.server_handler.upload_manager = new_manager

            logging.info("✅ 上传服务重启完成")
            logging.info("💡 现在可以尝试重新上传待上传记录")

        except Exception as e:
            logging.error(f"重启上传服务异常: {str(e)}")

    def _auto_handle_pending_records(self, pending_count):
        """自动处理未上传记录的智能流程"""
        try:
            logging.info(f"🤖 开始自动处理 {pending_count} 条未上传记录...")

            # 步骤1: 验证当前状态
            logging.info("📋 步骤1: 验证当前上传状态...")
            self.verify_upload_status()

            # 步骤2: 强制同步状态
            logging.info("📋 步骤2: 强制同步状态...")
            self.force_sync_all_status()

            # 重新统计
            current_pending = self._count_pending_records()
            if current_pending == 0:
                logging.info("✅ 状态同步后所有记录已完成")
                return

            logging.info(f"📊 状态同步后仍有 {current_pending} 条记录待处理")

            # 步骤2: 检查是否需要重启上传服务
            if current_pending > pending_count * 0.8:  # 如果超过80%记录仍未处理
                logging.info("🔄 步骤2: 自动重启上传服务...")
                self.restart_upload_service()

                # 等待服务重启
                import time
                time.sleep(3)

                # 步骤3: 重新上传
                logging.info("📤 步骤3: 重新上传待处理记录...")
                self.force_reupload_pending_records()

                # 等待上传完成
                time.sleep(5)

                # 最终检查
                final_pending = self._count_pending_records()
                if final_pending == 0:
                    logging.info("✅ 自动处理完成，所有记录已上传")
                elif final_pending < current_pending:
                    logging.info(f"✅ 自动处理部分成功，剩余 {final_pending} 条记录")
                    logging.info("💡 剩余记录可能需要手动检查网络或API服务器状态")
                else:
                    logging.warning(f"⚠️ 自动处理效果有限，仍有 {final_pending} 条记录")
                    logging.info("💡 建议检查网络连接和API服务器状态")
            else:
                logging.info("✅ 大部分记录已通过状态同步解决")

        except Exception as e:
            logging.error(f"自动处理异常: {str(e)}")

    def _count_pending_records(self):
        """统计当前待上传记录数量"""
        try:
            pending_count = 0
            total_items = self.tree.topLevelItemCount()

            for i in range(total_items):
                item = self.tree.topLevelItem(i)
                if item and item.text(3) == "待上传":
                    pending_count += 1

            return pending_count
        except Exception as e:
            logging.error(f"统计待上传记录异常: {str(e)}")
            return 0

    def verify_upload_status(self):
        """验证上传状态的准确性"""
        try:
            logging.info("🔍 开始验证上传状态...")

            # 统计界面状态
            total_items = self.tree.topLevelItemCount()
            ui_pending = 0
            ui_success = 0
            ui_failed = 0

            pending_records = []  # 收集待上传记录详情

            for i in range(total_items):
                item = self.tree.topLevelItem(i)
                if item:
                    status = item.text(3)
                    chip_id = item.text(0)
                    scan_time = item.text(2)

                    if status == "待上传":
                        ui_pending += 1
                        pending_records.append({
                            'chip_id': chip_id,
                            'scan_time': scan_time,
                            'ui_index': i
                        })
                    elif status == "已成功":
                        ui_success += 1
                    elif status == "已失败":
                        ui_failed += 1

            logging.info(f"📊 界面状态统计:")
            logging.info(f"   总记录: {total_items}")
            logging.info(f"   待上传: {ui_pending}")
            logging.info(f"   已成功: {ui_success}")
            logging.info(f"   已失败: {ui_failed}")

            # 检查上传管理器状态
            if self.server_handler and hasattr(self.server_handler, 'upload_manager'):
                upload_manager = self.server_handler.upload_manager
                if upload_manager:
                    self._check_upload_manager_queues(upload_manager)

            # 分析待上传记录
            if pending_records:
                logging.info(f"🔍 分析 {len(pending_records)} 条待上传记录:")
                self._analyze_pending_records_detailed(pending_records)
            else:
                logging.info("✅ 没有待上传记录")

            return ui_pending

        except Exception as e:
            logging.error(f"验证上传状态异常: {str(e)}")
            return 0

    def _check_upload_manager_queues(self, upload_manager):
        """检查上传管理器队列状态"""
        try:
            upload_queue_size = 0
            retry_queue_size = 0

            # 检查上传队列
            if hasattr(upload_manager, 'upload_mutex') and hasattr(upload_manager, 'upload_queue'):
                if upload_manager.upload_mutex.tryLock():
                    try:
                        upload_queue_size = len(upload_manager.upload_queue)
                        if upload_queue_size > 0:
                            # 显示队列中的前几个记录
                            queue_chips = []
                            for i, record in enumerate(upload_manager.upload_queue):
                                if i < 3:  # 只显示前3个
                                    queue_chips.append(getattr(record, 'chip_id', 'Unknown'))
                            logging.info(f"📋 上传队列中的记录: {queue_chips}{'...' if upload_queue_size > 3 else ''}")
                    finally:
                        upload_manager.upload_mutex.unlock()

            # 检查重试队列
            if hasattr(upload_manager, 'retry_mutex') and hasattr(upload_manager, 'retry_queue'):
                if upload_manager.retry_mutex.tryLock():
                    try:
                        retry_queue_size = len(upload_manager.retry_queue)
                        if retry_queue_size > 0:
                            # 显示重试队列中的前几个记录
                            retry_chips = []
                            for i, record in enumerate(upload_manager.retry_queue):
                                if i < 3:
                                    retry_chips.append(getattr(record, 'chip_id', 'Unknown'))
                            logging.info(f"🔄 重试队列中的记录: {retry_chips}{'...' if retry_queue_size > 3 else ''}")
                    finally:
                        upload_manager.retry_mutex.unlock()

            logging.info(f"📊 上传管理器队列状态:")
            logging.info(f"   上传队列: {upload_queue_size} 条")
            logging.info(f"   重试队列: {retry_queue_size} 条")

            # 关键判断
            if upload_queue_size == 0 and retry_queue_size == 0:
                logging.info("💡 队列为空 → 界面的'待上传'记录很可能是状态更新延迟")
            else:
                logging.info("💡 队列非空 → 界面的'待上传'记录可能确实未上传")

        except Exception as e:
            logging.error(f"检查上传管理器队列异常: {str(e)}")

    def _analyze_pending_records_detailed(self, pending_records):
        """详细分析待上传记录"""
        try:
            from datetime import datetime

            # 按时间分析
            now = datetime.now()
            very_old = 0  # 超过5分钟
            old = 0       # 1-5分钟
            recent = 0    # 1分钟内

            for record in pending_records[:10]:  # 只分析前10个
                try:
                    scan_time_str = record['scan_time']
                    if len(scan_time_str) >= 19:
                        scan_time = datetime.strptime(scan_time_str[:19], "%Y-%m-%d %H:%M:%S")
                        age_minutes = (now - scan_time).total_seconds() / 60

                        if age_minutes > 5:
                            very_old += 1
                        elif age_minutes > 1:
                            old += 1
                        else:
                            recent += 1

                        logging.debug(f"📅 {record['chip_id']}: {age_minutes:.1f}分钟前")
                except:
                    pass

            logging.info(f"⏰ 待上传记录时间分布:")
            logging.info(f"   超过5分钟: {very_old} 条 (很可能是状态延迟)")
            logging.info(f"   1-5分钟: {old} 条 (可能是状态延迟)")
            logging.info(f"   1分钟内: {recent} 条 (可能确实未上传)")

            # 显示具体示例
            sample_count = min(5, len(pending_records))
            logging.info(f"📝 待上传记录示例 (前{sample_count}条):")
            for i in range(sample_count):
                record = pending_records[i]
                logging.info(f"   {i+1}. {record['chip_id']} - {record['scan_time']}")

        except Exception as e:
            logging.error(f"分析待上传记录异常: {str(e)}")

    def test_ui_method(self):
        """测试UI方法是否正常工作"""
        try:
            logging.info("🧪 测试UI方法...")
            self._process_and_refresh_ui()
            logging.info("✅ UI方法测试成功")
        except Exception as e:
            logging.error(f"❌ UI方法测试失败: {str(e)}")
            import traceback
            logging.error(f"详细错误: {traceback.format_exc()}")

    def closeEvent(self, event):
        """窗口关闭事件处理"""
        try:
            # 如果有后台停止线程在运行，等待其完成
            if self.stop_thread and self.stop_thread.isRunning():
                logging.info("等待后台停止线程完成...")
                self.stop_thread.wait(3000)  # 最多等待3秒

            # 停止服务器
            if self.server_handler:
                self.server_handler.stop()

            event.accept()
        except Exception as e:
            logging.error(f"关闭窗口异常: {str(e)}")
            event.accept()


    def closeEvent(self, event):
        """重写关闭事件，服务运行时禁止关闭窗口"""
        if self.server_handler and self.server_handler.running:
            QMessageBox.warning(self, "提示", "服务正在运行，请先停止服务后再关闭系统！")
            event.ignore()
        else:
            event.accept()

