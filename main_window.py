from PyQt5.QtWidgets import QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QTreeWidget, QTreeWidgetItem, QMessageBox, QFrame, QHeaderView
from PyQt5.QtCore import Qt, QTimer, QMutex, QMutexLocker
from PyQt5.QtGui import QIcon, QColor
from tcp_server_handler import TcpServerHandler
from config import PORT, MAX_UI_RECORDS
import logging
from collections import deque

# 主窗口模块：基于PyQt5实现的主界面，负责UI布局、用户交互、数据显示与刷新、服务控制等
# 包含MainWindow类，集成了界面初始化、统计信息、设备列表、数据表格等功能
class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        # 设置窗口左上角图标
        self.setWindowIcon(QIcon("hechuang.ico"))
        self.server_handler = None
        self.data_items = {}

        # UI刷新优化相关变量
        self.update_buffer = deque()
        self.buffer_mutex = QMutex()
        self.last_total_scans = 0
        self.last_unique_chips = 0
        self.last_device_list_text = ""

        # 缓冲区监控
        self.buffer_stats = {
            'max_size': 0,
            'total_cleared': 0,
            'last_clear_time': None
        }

        self.tree = QTreeWidget()
        self.tree.setHeaderLabels(["芯片编号", "设备编号", "扫描时间", "上传状态"])
        self.tree.setStyleSheet("""
            QTreeWidget {
                font-size: 13px;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                background: white;
            }
            QTreeWidget::item {
                border-bottom: 1px solid #eee;
                padding: 10px 0;
            }
            QTreeWidget::item:selected {
                background: #2196F3;
                color: white;
            }
        """)
        self.tree.header().setStyleSheet("""
            QHeaderView::section {
                background-color: #607D8B;
                color: white;
                padding: 12px;
                border: none;
                font-size: 13px;
            }
        """)
        self.tree.header().setSectionResizeMode(QHeaderView.Stretch)
        self.tree.setAlternatingRowColors(True)

        self._init_ui()
        self._init_timers()

    def _init_ui(self):
        self.setWindowTitle("合创体育 v4.0.2")
        self.setGeometry(100, 100, 1280, 720)

        # 主容器
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)

        # ===== 状态栏 =====
        status_frame = QFrame()
        status_layout = QHBoxLayout(status_frame)
        status_layout.setContentsMargins(10, 5, 10, 5)

        self.server_status = QLabel("🔴 服务未运行")
        self.server_status.setStyleSheet("font-size: 14px; color: #666;")
        self.port_label = QLabel(f"端口：{PORT}")
        self.port_label.setStyleSheet("font-size: 14px; color: #666;")

        status_layout.addWidget(self.server_status)
        status_layout.addStretch()
        status_layout.addWidget(self.port_label)
        main_layout.addWidget(status_frame)

        # ===== 统计面板 =====
        stats_frame = QFrame()
        stats_layout = QHBoxLayout(stats_frame)
        stats_layout.setSpacing(20)

        self.scan_count = self._create_stat_card("0", "总扫描次数", "#4CAF50")
        self.participant_count = self._create_stat_card("0", "人数", "#2196F3")
        self.device_count = self._create_stat_card("0", "在线设备", "#9C27B0")

        stats_layout.addWidget(self.scan_count)
        stats_layout.addWidget(self.participant_count)
        stats_layout.addWidget(self.device_count)
        main_layout.addWidget(stats_frame)

        # ===== 数据表格 =====
        self.tree.setStyleSheet("""
            QTreeWidget {
                font-size: 13px;
            }
            QTreeWidget::item {
                height: 28px;
            }
        """)
        self.tree.header().setSectionResizeMode(QHeaderView.Stretch)
        self.tree.setAlternatingRowColors(True)

        # 优化树形控件性能
        self.tree.setUniformRowHeights(True)  # 统一行高，提高性能
        self.tree.setRootIsDecorated(False)  # 不显示根节点装饰
        self.tree.setSortingEnabled(False)  # 禁用排序，提高性能
        main_layout.addWidget(self.tree)

        # ===== 控制面板 =====
        control_frame = QFrame()
        control_layout = QHBoxLayout(control_frame)
        self.start_btn = QPushButton("启动服务")
        self.start_btn.setFixedSize(120, 40)
        self.start_btn.setStyleSheet("""
            QPushButton {
                background: #4CAF50;
                color: white;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background: #45a049;
            }
        """)
        self.start_btn.clicked.connect(self.toggle_server)
        control_layout.addWidget(self.start_btn)
        main_layout.addWidget(control_frame)

        # ===== 设备面板 =====
        device_frame = QFrame()
        device_layout = QVBoxLayout(device_frame)
        device_layout.setContentsMargins(10, 10, 10, 10)

        title = QLabel("📡 在线设备列表")
        title.setStyleSheet("font-size: 16px; color: #333;")
        self.device_list = QLabel("无活动连接")
        self.device_list.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #666;
                padding: 10px;
                background: #f8f9fa;
                border-radius: 6px;
                min-height: 60px;
            }
        """)
        self.device_list.setWordWrap(True)

        device_layout.addWidget(title)
        device_layout.addWidget(self.device_list)
        main_layout.addWidget(device_frame)

    def _create_stat_card(self, value, title, color):
        """创建统计卡片"""
        frame = QFrame()
        frame.setStyleSheet(f"""
            background: white;
            border-radius: 8px;
            border: 1px solid #eee;
        """)
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(20, 15, 20, 15)

        value_label = QLabel(value)
        value_label.setStyleSheet(f"""
            font-size: 28px; 
            font-weight: bold; 
            color: {color};
            qproperty-alignment: AlignCenter;
        """)

        title_label = QLabel(title)
        title_label.setStyleSheet("""
            font-size: 14px; 
            color: #666;
            qproperty-alignment: AlignCenter;
        """)

        layout.addWidget(value_label)
        layout.addWidget(title_label)

        return frame

    def _init_timers(self):
        """初始化定时器"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self._process_and_refresh_ui)
        self.update_timer.setInterval(300)  # 300ms批量刷新一次UI (平衡性能和响应性)
        self.update_timer.start()

    def handle_data_update(self, records):
        """处理数据更新（将记录添加到缓冲区）"""
        if not records:
            return

        try:
            # 如果是单个记录，转换为列表
            if not isinstance(records, list):
                records = [records]

            # 非阻塞方式添加到缓冲区
            if self.buffer_mutex.tryLock():
                try:
                    for record in records:
                        # 限制缓冲区大小，防止内存溢出和UI阻塞
                        if len(self.update_buffer) > 200:  # 适当增加缓冲区大小
                            # 移除最旧的记录，保留最新的
                            removed_count = 0
                            while len(self.update_buffer) > 100:
                                if self.update_buffer:
                                    self.update_buffer.popleft()
                                    removed_count += 1
                                else:
                                    break
                            if removed_count > 0:
                                from datetime import datetime
                                self.buffer_stats['total_cleared'] += removed_count
                                self.buffer_stats['last_clear_time'] = datetime.now()
                                logging.info(f"UI缓冲区清理: 移除 {removed_count} 条旧记录，当前大小: {len(self.update_buffer)}，累计清理: {self.buffer_stats['total_cleared']}")
                        self.update_buffer.append(record)

                        # 更新缓冲区大小统计
                        current_size = len(self.update_buffer)
                        if current_size > self.buffer_stats['max_size']:
                            self.buffer_stats['max_size'] = current_size
                finally:
                    self.buffer_mutex.unlock()
            else:
                # 如果无法获取锁，直接丢弃数据，避免阻塞
                logging.warning("UI缓冲区忙，丢弃数据更新")

        except Exception as e:
            logging.error(f"处理数据更新异常: {str(e)}")

        # 不再立即调用UI更新，完全依赖定时器

    def _process_and_refresh_ui(self):
        """处理缓冲区记录并刷新UI统计信息"""
        try:
            MAX_UI_DISPLAY = 20  # 适当增加单次处理数量，提高处理效率
            records_to_process = []

            # 使用非阻塞锁
            if not self.buffer_mutex.tryLock():
                return  # 如果无法获取锁，跳过这次更新

            try:
                # 只处理少量记录，避免UI卡顿
                process_count = min(len(self.update_buffer), MAX_UI_DISPLAY)
                for _ in range(process_count):
                    if self.update_buffer:
                        records_to_process.append(self.update_buffer.popleft())
            finally:
                self.buffer_mutex.unlock()

            if not records_to_process:
                return
            self.tree.setUpdatesEnabled(False)
            new_item_added = False
            records_to_add_to_tree = []
            processed_count = 0

            # 添加处理时间限制，避免UI长时间阻塞
            import time
            start_time = time.time()
            max_process_time = 0.1  # 最多处理100ms，提高处理效率

            for record in records_to_process:
                try:
                    # 检查处理时间，避免UI长时间阻塞
                    if time.time() - start_time > max_process_time:
                        logging.debug(f"UI处理超时，已处理 {processed_count} 条记录")
                        break

                    if processed_count >= MAX_UI_DISPLAY:
                        # 不再放回缓冲区，直接丢弃，避免死锁
                        break

                    # 验证记录的有效性
                    if not hasattr(record, 'chip_id') or not hasattr(record, 'scan_time'):
                        logging.warning(f"无效的记录对象: {record}")
                        continue

                    key = f"{record.chip_id}-{record.scan_time}"
                    status = getattr(record, 'status', '待上传')  # 默认状态

                    if key not in self.data_items:
                        item = QTreeWidgetItem()
                        item.setText(0, str(record.chip_id))
                        item.setText(1, str(getattr(record, 'device_id', '')))
                        item.setText(2, str(record.scan_time))
                        item.setText(3, self._status_text(status))
                        for col in range(4):  # 包括状态列
                            item.setTextAlignment(col, Qt.AlignCenter)
                        records_to_add_to_tree.append(item)
                        self.data_items[key] = item
                        new_item_added = True
                    else:
                        # 更新现有记录的状态
                        item = self.data_items[key]
                        item.setText(3, self._status_text(status))
                    processed_count += 1

                except Exception as e:
                    logging.error(f"处理单个记录异常: {str(e)}, 记录: {record}")
                    continue
            if records_to_add_to_tree:
                self.tree.addTopLevelItems(records_to_add_to_tree)

                # 限制显示的记录数量，自动清理旧记录
                if self.tree.topLevelItemCount() > MAX_UI_RECORDS:
                    items_to_remove = self.tree.topLevelItemCount() - MAX_UI_RECORDS
                    for i in range(items_to_remove):
                        item = self.tree.takeTopLevelItem(0)  # 移除最旧的记录
                        if item:
                            # 从data_items中也移除
                            for key, stored_item in list(self.data_items.items()):
                                if stored_item == item:
                                    del self.data_items[key]
                                    break
                    logging.debug(f"自动清理了 {items_to_remove} 条旧记录")

            if new_item_added:
                self.tree.scrollToBottom()
            self.tree.setUpdatesEnabled(True)
        except Exception as e:
            import traceback
            logging.error(f"UI更新异常: {str(e)}")
            logging.error(f"详细错误信息: {traceback.format_exc()}")
            # 确保UI状态正确
            try:
                self.tree.setUpdatesEnabled(True)
            except:
                pass

        # 简化统计信息更新，避免跨线程锁竞争
        try:
            if self.server_handler:
                # 使用非阻塞方式获取统计信息
                try:
                    current_total_scans = len(self.server_handler.data_records)
                    if current_total_scans != self.last_total_scans:
                        self.scan_count.layout().itemAt(0).widget().setText(str(current_total_scans))
                        self.last_total_scans = current_total_scans
                except:
                    pass  # 忽略统计错误，避免界面死锁

                try:
                    # 使用非阻塞锁获取芯片统计
                    if self.server_handler.chip_mutex.tryLock():
                        try:
                            current_unique_chips = len(self.server_handler.unique_chips)
                            if current_unique_chips != self.last_unique_chips:
                                self.participant_count.layout().itemAt(0).widget().setText(str(current_unique_chips))
                                self.last_unique_chips = current_unique_chips
                        finally:
                            self.server_handler.chip_mutex.unlock()
                except:
                    pass  # 忽略统计错误
                current_device_text = self.device_list.text()
                if current_device_text != self.last_device_list_text:
                    device_count_val = 0
                    if current_device_text != "无活动连接" and current_device_text.strip():
                        device_count_val = len(current_device_text.split("\n"))
                    self.device_count.layout().itemAt(0).widget().setText(str(device_count_val))
                    self.last_device_list_text = current_device_text
        except Exception as e:
            logging.error(f"UI statistics refresh failed: {str(e)}")
            pass # Avoid crashing UI on stats refresh error

    def _status_text(self, status):
        """状态显示文本"""
        status_map = {
            "待上传": ("待上传", "#FF9800"),
            "上传中": ("上传中...", "#2196F3"),
            "已成功": ("✓ 成功", "#4CAF50"),
            "已失败": ("✗ 失败", "#F44336")
        }
        text, color = status_map.get(status, ("未知", "#9E9E9E"))
        #return f'<span style="color: {color}; font-weight: 500;">{text}</span>'
        return f'{text}'

    def handle_connection_msg(self, msg):
        """处理设备连接消息"""
        try:
            logging.info(f"收到连接消息: {msg}")  # 添加调试日志

            current_devices = self.device_list.text().split("\n")
            if "无活动连接" in current_devices:
                current_devices = []

            # 处理连接消息
            if "已连接:" in msg:
                device = msg.split(":", 1)[1].strip()
                if device not in current_devices:
                    current_devices.append(device)
                    logging.info(f"设备上线: {device}")
            elif "已断开:" in msg:
                device = msg.split(":", 1)[1].strip()
                if device in current_devices:
                    current_devices.remove(device)
                    logging.info(f"设备下线: {device}")
            # 兼容旧格式
            elif "读卡器地址:" in msg:
                device = msg.split(":", 1)[1].strip()
                if device not in current_devices:
                    current_devices.append(device)
                    logging.info(f"设备上线(旧格式): {device}")
            elif "读卡器断开:" in msg:
                device = msg.split(":", 1)[1].strip()
                if device in current_devices:
                    current_devices.remove(device)
                    logging.info(f"设备下线(旧格式): {device}")
            else:
                logging.warning(f"未识别的连接消息格式: {msg}")

            # 更新设备列表显示
            device_text = "\n".join(current_devices) if current_devices else "无活动连接"
            self.device_list.setText(device_text)
            logging.info(f"设备列表已更新: {device_text}")

            # 立即更新设备数量统计
            device_count = len(current_devices) if current_devices else 0
            self.device_count.layout().itemAt(0).widget().setText(str(device_count))

        except Exception as e:
            logging.error(f"连接消息处理失败: {str(e)}")

    def handle_status_update(self, records):
        """处理批量上传状态更新（减少UI阻塞）"""
        try:
            if not records:
                return

            # 批量处理状态更新，减少UI重绘次数
            self.tree.setUpdatesEnabled(False)
            updated_count = 0

            try:
                for record in records:
                    key = f"{record.chip_id}-{record.scan_time}"

                    # 查找对应的界面项目并更新状态
                    if key in self.data_items:
                        item = self.data_items[key]
                        old_status = item.text(3)
                        new_status = record.status

                        # 只有状态真正改变时才更新
                        if old_status != new_status:
                            item.setText(3, new_status)
                            updated_count += 1

                            # 根据状态设置不同的字体颜色
                            if new_status == "已成功":
                                item.setForeground(3, QColor(0, 128, 0))      # 绿色字体
                            elif new_status == "已失败":
                                item.setForeground(3, QColor(255, 0, 0))      # 红色字体
                            elif new_status == "重试失败":
                                item.setForeground(3, QColor(128, 0, 128))    # 紫色字体
                            else:
                                item.setForeground(3, QColor(0, 0, 0))        # 黑色字体（默认）

                if updated_count > 0:
                    logging.debug(f"批量状态更新完成: {updated_count} 条记录")

            finally:
                self.tree.setUpdatesEnabled(True)

        except Exception as e:
            logging.error(f"批量状态更新处理失败: {str(e)}")
            # 确保UI状态正确
            try:
                self.tree.setUpdatesEnabled(True)
            except:
                pass

    def toggle_server(self):
        """切换服务器状态"""
        if self.server_handler and self.server_handler.running:
            self._stop_server()
        else:
            self._start_server()

    def _start_server(self):
        """启动服务器"""
        try:
            self.server_handler = TcpServerHandler(self)
            self.server_handler.update_signal.connect(self.handle_data_update, Qt.QueuedConnection)
            self.server_handler.connection_update_signal.connect(self.handle_connection_msg, Qt.QueuedConnection)

            # 启动服务器（这会创建上传管理器）
            self.server_handler.start()

            # 连接上传管理器的状态更新信号（在启动后连接）
            if hasattr(self.server_handler, 'upload_manager') and self.server_handler.upload_manager:
                self.server_handler.upload_manager.status_update_signal.connect(self.handle_status_update, Qt.QueuedConnection)
            self.server_status.setText("🟢 服务运行中")
            self.start_btn.setText("停止服务")
            self.start_btn.setStyleSheet("background: #f44336; color: white;")
            self.tree.clear()
            self.data_items.clear()
            self.last_total_scans = 0
            self.last_unique_chips = 0
            self.last_device_list_text = ""
            self.device_list.setText("无活动连接")
            # Manually trigger a refresh to show 0 counts initially for stats
            self._process_and_refresh_ui() 
            logging.info("服务器启动成功")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"服务启动失败: {str(e)}")

    def _stop_server(self):
        """停止服务器"""
        if self.server_handler:
            # 处理剩余的UI缓冲区数据
            self._process_and_refresh_ui()
            logging.info("已处理剩余的UI数据")

            self.server_handler.stop()
        self.server_status.setText("🔴 服务未运行")
        self.start_btn.setText("启动服务")
        self.start_btn.setStyleSheet("background: #4CAF50; color: white;")
        self.device_list.setText("无活动连接")
        logging.info("服务器已停止")


    def closeEvent(self, event):
        """重写关闭事件，服务运行时禁止关闭窗口"""
        if self.server_handler and self.server_handler.running:
            QMessageBox.warning(self, "提示", "服务正在运行，请先停止服务后再关闭系统！")
            event.ignore()
        else:
            event.accept()

