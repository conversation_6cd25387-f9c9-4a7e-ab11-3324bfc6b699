# 程序入口模块：负责初始化应用、设置全局样式和调色板，并启动主窗口
import sys
import logging
from PyQt5.QtWidgets import QApplication
from PyQt5.QtGui import QFont, QPalette, QColor
from main_window import MainWindow



if __name__ == "__main__":
    # 配置日志系统
    logging.basicConfig(
        level=logging.INFO,  # 设置日志级别为INFO，显示上传相关日志
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),  # 输出到终端
            logging.FileHandler('server.log', encoding='utf-8')  # 同时保存到文件
        ]
    )

    # 设置特定模块的日志级别
    logging.getLogger('upload_manager').setLevel(logging.INFO)
    logging.getLogger('api_client').setLevel(logging.INFO)
    logging.getLogger('tcp_server_handler').setLevel(logging.INFO)

    logging.info("=" * 50)
    logging.info("合创V4 RFID数据处理系统启动")
    logging.info("=" * 50)

    app = QApplication(sys.argv)
    app.setStyle("Fusion")

    # 设置全局字体
    font = QFont("微软雅黑", 9)
    app.setFont(font)

    # 设置调色板
    palette = QPalette()
    palette.setColor(QPalette.Window, QColor(245, 245, 245))
    app.setPalette(palette)

    window = MainWindow()
    window.show()
    sys.exit(app.exec_())