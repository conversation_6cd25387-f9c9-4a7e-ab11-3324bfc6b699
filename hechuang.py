# 程序入口模块：负责初始化应用、设置全局样式和调色板，并启动主窗口
import sys
import logging
from PyQt5.QtWidgets import QApplication
from PyQt5.QtGui import QFont, QPalette, QColor
from main_window import MainWindow



if __name__ == "__main__":
    # 配置日志系统 - 这对诊断上传问题很重要
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),  # 输出到终端
            logging.FileHandler('server.log', encoding='utf-8')
        ]
    )

    # 设置模块日志级别
    logging.getLogger('upload_manager').setLevel(logging.INFO)
    logging.getLogger('api_client').setLevel(logging.INFO)

    logging.info("🚀 合创V4 RFID数据处理系统启动")

    # 添加中断信号处理
    import signal

    def signal_handler(signum, frame):
        logging.info(f"收到中断信号 {signum}，正在优雅关闭...")
        app.quit()

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    app = QApplication(sys.argv)
    app.setStyle("Fusion")

    # 设置全局字体
    font = QFont("微软雅黑", 9)
    app.setFont(font)

    # 设置调色板
    palette = QPalette()
    palette.setColor(QPalette.Window, QColor(245, 245, 245))
    app.setPalette(palette)

    window = MainWindow()
    try:
        window.show()

        logging.info("✅ 系统启动完成")
        sys.exit(app.exec_())

    except KeyboardInterrupt:
        logging.info("用户中断程序")
        sys.exit(0)
    except Exception as e:
        logging.error(f"程序异常退出: {str(e)}")
        import traceback
        logging.error(f"详细错误: {traceback.format_exc()}")
        sys.exit(1)