# TCP服务器处理模块：负责TCP服务端的启动、客户端连接管理、消息接收与解析、数据上传和重试机制
# 主要类TcpServerHandler，集成了多线程、信号通信、线程安全等功能
import socket
import threading
import logging
from datetime import datetime
from collections import deque
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
from PyQt5.QtCore import QObject, pyqtSignal, QTimer, QMutex, QMutexLocker
import sqlite3
import os
from data_record import DataRecord
from api_client import APIClient
from config import (
    PORT, MESSAGE_LENGTH, RETRY_INTERVAL, DB_PATH, MAX_MEMORY_RECORDS,
    MAX_RETRY_ATTEMPTS, MAX_CONNECTIONS, SOCKET_RECV_BUFFER, SOCKET_SEND_BUFFER,
    CONNECTION_TIMEOUT, MAX_CONSECUTIVE_ERRORS, MESSAGE_RECEIVE_TIMEOUT,
    NETWORK_THREADS, DATA_PROCESSING_THREADS, API_THREADS, RETRY_THREADS,
    API_BATCH_SIZE, API_BATCH_INTERVAL, UI_UPDATE_INTERVAL, STATS_LOG_INTERVAL,
    DEBUG_MESSAGE_FORMAT, MAX_BUFFER_SIZE
)

class TcpServerHandler(QObject):
    update_signal = pyqtSignal(object)  # 发送DataRecord对象列表或单个对象
    connection_update_signal = pyqtSignal(str)

    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.server_socket = None
        self.running = False
        self.data_records = deque(maxlen=MAX_MEMORY_RECORDS)  # 限制内存中记录数量
        self.retry_queue = deque()  # 单独的重试队列
        self.api_client = APIClient() # 初始化API客户端

        self.unique_chips = set()
        self.client_sockets = []

        self.data_mutex = QMutex()
        self.retry_mutex = QMutex()  # 重试队列的互斥锁
        self.chip_mutex = QMutex()
        self.conn_mutex = QMutex()

        # 优化线程池配置以提高并发处理能力
        self.network_executor = ThreadPoolExecutor(max_workers=NETWORK_THREADS, thread_name_prefix="Network")
        self.data_processing_executor = ThreadPoolExecutor(max_workers=DATA_PROCESSING_THREADS, thread_name_prefix="DataProc")
        self.retry_executor = ThreadPoolExecutor(max_workers=RETRY_THREADS, thread_name_prefix="Retry")
        self.api_executor = ThreadPoolExecutor(max_workers=API_THREADS, thread_name_prefix="API")

        self.retry_timer = QTimer()
        self.retry_timer.timeout.connect(self.process_retry)

        # UI批量更新机制
        self.update_buffer = [] 
        self.update_buffer_mutex = QMutex()
        self.update_timer = QTimer()
        self.update_timer.setInterval(UI_UPDATE_INTERVAL)  # UI更新间隔
        self.update_timer.timeout.connect(self._emit_batch_updates)

        # 批量API上传队列和定时器
        self.api_batch_queue = deque()
        self.api_batch_mutex = QMutex()
        self.api_batch_timer = QTimer()
        self.api_batch_timer.setInterval(API_BATCH_INTERVAL)  # API批量发送间隔
        self.api_batch_timer.timeout.connect(self._flush_api_batch)
        self.api_batch_timer.start()

        # 流量统计和监控
        self.stats = {
            'total_connections': 0,
            'active_connections': 0,
            'total_messages': 0,
            'failed_messages': 0,
            'last_message_time': None,
            'messages_per_second': 0,
            'last_stats_time': datetime.now()
        }
        self.stats_mutex = QMutex()

        # 统计定时器
        self.stats_timer = QTimer()
        self.stats_timer.setInterval(STATS_LOG_INTERVAL)  # 统计日志间隔
        self.stats_timer.timeout.connect(self._log_statistics)
        self.stats_timer.start()

    def _accept_connections(self):
        """接受客户端连接"""
        while self.running:
            try:
                client_socket, addr = self.server_socket.accept()
                addr_str = f"{addr[0]}:{addr[1]}"
                logging.info(f"客户端连接: {addr_str}")

                # 检查连接数限制，防止资源耗尽
                with QMutexLocker(self.conn_mutex):
                    if len(self.client_sockets) >= MAX_CONNECTIONS:  # 限制最大连接数
                        logging.warning(f"连接数已达上限，拒绝新连接: {addr_str}")
                        try:
                            client_socket.close()
                        except:
                            pass
                        continue

                    self.client_sockets.append(client_socket)

                # 更新连接统计
                with QMutexLocker(self.stats_mutex):
                    self.stats['total_connections'] += 1
                    self.stats['active_connections'] = len(self.client_sockets)

                self.connection_update_signal.emit(f"已连接: {addr_str}")

                # 启动线程处理客户端消息
                self.network_executor.submit(
                    self._handle_client,
                    client_socket,
                    addr_str
                )

            except socket.error as e:
                if self.running:
                    logging.error(f"Socket接受连接错误: {str(e)}")
                    # 短暂休眠避免CPU占用过高
                    import time
                    time.sleep(0.1)
            except Exception as e:
                if self.running:
                    logging.error(f"接受连接异常: {str(e)}")
                break

    def start(self):
        """启动TCP服务器"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)

            # 优化服务器socket配置以减少丢包
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)  # 允许地址重用
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, 1024 * 1024)  # 1MB接收缓冲区
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 1024 * 1024)  # 1MB发送缓冲区

            # 设置TCP选项以提高性能
            if hasattr(socket, 'TCP_NODELAY'):
                self.server_socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)  # 禁用Nagle算法

            self.server_socket.bind(('', PORT))
            self.server_socket.listen(128)  # 增加连接队列长度，支持更多并发连接
            self.running = True

            # 启动定时器
            self.retry_timer.start(RETRY_INTERVAL * 1000)
            self.update_timer.start()

            # 启动连接接受线程
            self.network_executor.submit(self._accept_connections)

            logging.info(f"TCP服务器已启动 - 端口: {PORT}, 连接队列: 128")
        except Exception as e:
            logging.error(f"服务器启动失败: {str(e)}")
            raise

    def _handle_client(self, client_socket, addr_str):
        """处理客户端连接"""
        consecutive_errors = 0
        max_consecutive_errors = MAX_CONSECUTIVE_ERRORS
        buffer = b''  # 用于处理TCP流数据

        try:
            # 优化socket选项以提高性能和可靠性
            client_socket.settimeout(CONNECTION_TIMEOUT)  # 连接超时时间
            client_socket.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
            client_socket.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, SOCKET_RECV_BUFFER)  # 接收缓冲区
            client_socket.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, SOCKET_SEND_BUFFER)  # 发送缓冲区

            # 设置TCP选项
            if hasattr(socket, 'TCP_NODELAY'):
                client_socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
            if hasattr(socket, 'SO_KEEPALIVE'):
                client_socket.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)

            logging.info(f"客户端连接已优化: {addr_str}")

            while self.running:
                try:
                    # 接收数据到缓冲区
                    data = client_socket.recv(4096)
                    if not data:
                        logging.info(f"客户端正常断开: {addr_str}")
                        break

                    buffer += data

                    # 从缓冲区中提取完整消息
                    messages = self._extract_messages_from_buffer(buffer)
                    if messages is None:
                        # 缓冲区数据不足，继续接收
                        continue

                    buffer, extracted_messages = messages

                    # 重置错误计数器
                    consecutive_errors = 0

                    # 处理提取的消息
                    for message in extracted_messages:
                        self.data_processing_executor.submit(self._process_message, message, addr_str)

                except socket.timeout:
                    consecutive_errors += 1
                    logging.warning(f"客户端超时 ({consecutive_errors}/{max_consecutive_errors}): {addr_str}")
                    if consecutive_errors >= max_consecutive_errors:
                        logging.error(f"客户端连续超时次数过多，断开连接: {addr_str}")
                        break
                    continue

                except socket.error as e:
                    consecutive_errors += 1
                    logging.error(f"Socket错误 ({consecutive_errors}/{max_consecutive_errors}): {addr_str} - {str(e)}")
                    if consecutive_errors >= max_consecutive_errors:
                        logging.error(f"客户端连续错误次数过多，断开连接: {addr_str}")
                        break

                    # 短暂休眠后重试
                    import time
                    time.sleep(0.1)

        except Exception as e:
            logging.error(f"客户端处理异常: {addr_str} - {str(e)}")
        finally:
            self._cleanup_client_connection(client_socket, addr_str)

    def _extract_messages_from_buffer(self, buffer):
        """从缓冲区中提取完整的消息"""
        try:
            messages = []
            remaining_buffer = buffer

            while len(remaining_buffer) >= MESSAGE_LENGTH:
                # 寻找消息开始标志 '#'
                start_pos = -1
                for i in range(len(remaining_buffer) - MESSAGE_LENGTH + 1):
                    if remaining_buffer[i:i+1] == b'#':
                        # 检查这个位置是否能构成完整消息
                        if i + MESSAGE_LENGTH <= len(remaining_buffer):
                            potential_message = remaining_buffer[i:i+MESSAGE_LENGTH]
                            try:
                                decoded = potential_message.decode('utf-8', errors='replace')
                                if self._is_valid_message_format(decoded):
                                    start_pos = i
                                    break
                            except:
                                continue

                if start_pos == -1:
                    # 没有找到有效的消息开始位置
                    if len(remaining_buffer) > MAX_BUFFER_SIZE:
                        # 如果缓冲区太大，丢弃一些数据避免内存问题
                        logging.warning(f"缓冲区过大，丢弃部分数据: {len(remaining_buffer)} 字节")
                        remaining_buffer = remaining_buffer[-MESSAGE_LENGTH:]
                    break

                # 如果开始位置不是0，说明前面有无效数据
                if start_pos > 0:
                    invalid_data = remaining_buffer[:start_pos]
                    logging.warning(f"丢弃无效数据 ({len(invalid_data)} 字节): {invalid_data[:20]}{'...' if len(invalid_data) > 20 else ''}")
                    remaining_buffer = remaining_buffer[start_pos:]

                # 提取消息
                message_bytes = remaining_buffer[:MESSAGE_LENGTH]
                message = message_bytes.decode('utf-8', errors='replace')

                if self._is_valid_message_format(message):
                    messages.append(message)
                    remaining_buffer = remaining_buffer[MESSAGE_LENGTH:]
                else:
                    # 消息格式无效，跳过这个字节继续寻找
                    remaining_buffer = remaining_buffer[1:]

            return remaining_buffer, messages

        except Exception as e:
            logging.error(f"提取消息异常: {str(e)}")
            return buffer, []

    def _is_valid_message_format(self, message):
        """验证消息格式是否有效"""
        try:
            if len(message) != MESSAGE_LENGTH:
                return False
            if not message.startswith('#'):
                return False
            # 可以添加更多格式验证
            return True
        except:
            return False

    def _receive_complete_message(self, sock):
        """接收完整的消息，确保数据完整性"""
        try:
            msg = b''
            start_time = datetime.now()
            max_receive_time = MESSAGE_RECEIVE_TIMEOUT  # 最大接收时间

            while len(msg) < MESSAGE_LENGTH:
                # 检查接收超时
                if (datetime.now() - start_time).total_seconds() > max_receive_time:
                    logging.warning(f"消息接收超时，已接收: {len(msg)}/{MESSAGE_LENGTH} 字节")
                    return None

                # 计算还需要接收的字节数
                remaining = MESSAGE_LENGTH - len(msg)

                # 使用较小的块大小以提高响应性
                chunk_size = min(remaining, 1024)
                chunk = sock.recv(chunk_size)

                if not chunk:
                    # 连接已关闭
                    if len(msg) > 0:
                        logging.warning(f"连接意外关闭，已接收: {len(msg)}/{MESSAGE_LENGTH} 字节")
                    return None

                msg += chunk

            # 验证消息长度
            if len(msg) != MESSAGE_LENGTH:
                logging.error(f"消息长度不匹配: 期望{MESSAGE_LENGTH}, 实际{len(msg)}")
                return None

            # 解码并返回完整消息
            decoded_msg = msg.decode('utf-8', errors='replace')

            # 基本格式验证
            if not decoded_msg.startswith('#'):
                logging.warning(f"消息格式异常，不以#开头: {decoded_msg[:10]}...")
                return None

            return decoded_msg

        except socket.timeout:
            raise  # 重新抛出超时异常
        except socket.error:
            raise  # 重新抛出socket错误
        except Exception as e:
            logging.error(f"接收消息异常: {str(e)}")
            return None

    def _cleanup_client_connection(self, client_socket, addr_str):
        """清理客户端连接资源"""
        try:
            with QMutexLocker(self.conn_mutex):
                if client_socket in self.client_sockets:
                    self.client_sockets.remove(client_socket)

            # 更新连接统计
            with QMutexLocker(self.stats_mutex):
                self.stats['active_connections'] = len(self.client_sockets)

            try:
                client_socket.shutdown(socket.SHUT_RDWR)
            except:
                pass
            client_socket.close()

            logging.info(f"客户端断开: {addr_str}")
            self.connection_update_signal.emit(f"已断开: {addr_str}")
        except Exception as e:
            logging.error(f"清理客户端连接异常: {str(e)}")

    def _process_message(self, message, addr_str):
        """处理消息内容"""
        # 更新消息统计
        with QMutexLocker(self.stats_mutex):
            self.stats['total_messages'] += 1
            self.stats['last_message_time'] = datetime.now()

        # 验证消息格式和长度（这里应该已经通过了_is_valid_message_format验证）
        logging.debug(f"处理消息 [{addr_str}]: {message[:20]}... (长度: {len(message)})")

        if not self._is_valid_message_format(message):
            logging.warning(f"消息格式验证失败 [{addr_str}]: {message[:20]}...")
            # 更新失败统计
            with QMutexLocker(self.stats_mutex):
                self.stats['failed_messages'] += 1
            return

        try:
            # 解析数据
            # 解析数据
            # 原始消息格式: #FC0011112506192129040000000000A00000086
            # 设备编号: FC001111 (从索引1开始，长度8)
            # EPC代码: (从索引25开始，长度6)
            device_number = message[1:9]  # 设备编号
            card_number = message[25:31]  # EPC代码
            now_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]  # 含毫秒的时间

            # 创建记录
            record = DataRecord(card_number, device_number, now_time)

            # 异步处理数据保存、API发送和添加到内存记录
            self.data_processing_executor.submit(self._process_data_async, record)

            # 批量更新UI (异步处理)
            self.data_processing_executor.submit(self._add_to_update_buffer_async, record)
        except Exception as e:
            logging.error(f"消息处理异常: {str(e)}")

    def _process_data_async(self, record):
        """异步处理数据和API发送"""
        # 将记录添加到内存记录
        with QMutexLocker(self.data_mutex):
            self.data_records.append(record)

        # 直接提交API批量队列
        self.api_executor.submit(self._send_to_api, record)

        logging.info(f"处理消息成功 | 设备: {record.device_id} | 芯片: {record.chip_id} | 时间: {record.scan_time}")

        # 更新芯片统计
        with QMutexLocker(self.chip_mutex):
            if record.chip_id not in self.unique_chips:
                self.unique_chips.add(record.chip_id)

    def _add_to_update_buffer_async(self, record):
        """异步将记录添加到UI更新缓冲区"""
        with QMutexLocker(self.update_buffer_mutex):
            self.update_buffer.append(record)

    def _send_to_api(self, record):
        """异步发送数据到API（批量上传队列）"""
        with QMutexLocker(self.api_batch_mutex):
            self.api_batch_queue.append(record)

    def process_retry(self):
        while self.retry_queue:
            record = self.retry_queue.popleft()
            logging.debug(f"DEBUG: Inside process_retry, type of record: {type(record)}")
            if not isinstance(record, DataRecord):
                logging.error(f"ERROR: process_retry received non-DataRecord object: {type(record)}")
                continue

            if record.retry_count < MAX_RETRY_ATTEMPTS:
                record.retry_count += 1
                record.last_retry = datetime.now()
                logging.info(f"重试上传: {record.chip_id}, 第 {record.retry_count} 次")
                self.api_executor.submit(self._send_to_api, record)
            else:
                record.status = "重试失败"
                with QMutexLocker(self.update_buffer_mutex):
                    self.update_buffer.append(record)

    def _emit_batch_updates(self):
        #""批量发送UI更新信号""
        with QMutexLocker(self.update_buffer_mutex):
            if self.update_buffer:
                # 发送批量更新信号，使用列表传递多个记录
                self.update_signal.emit(self.update_buffer.copy())
                self.update_buffer.clear()

    def stop(self):
        """停止服务器"""
        logging.info("开始停止服务器...")
        self.running = False

        # 停止定时器
        self.retry_timer.stop()
        self.update_timer.stop()
        self.api_batch_timer.stop()
        self.stats_timer.stop()

        # 关闭客户端连接
        with QMutexLocker(self.conn_mutex):
            logging.info(f"关闭 {len(self.client_sockets)} 个客户端连接")
            for sock in self.client_sockets:
                try:
                    sock.shutdown(socket.SHUT_RDWR)
                    sock.close()
                except Exception:
                    pass
            self.client_sockets.clear()

        # 关闭服务器socket
        if self.server_socket:
            try:
                self.server_socket.close()
                logging.info("服务器socket已关闭")
            except Exception:
                pass

        # 关闭所有线程池
        logging.info("关闭线程池...")
        executors = [
            ("Network", self.network_executor),
            ("DataProcessing", self.data_processing_executor),
            ("Retry", self.retry_executor),
            ("API", self.api_executor)
        ]

        for name, executor in executors:
            try:
                executor.shutdown(wait=False)
                logging.info(f"{name} 线程池已关闭")
            except Exception as e:
                logging.error(f"关闭 {name} 线程池异常: {str(e)}")

        logging.info("服务器已完全停止")

    def _log_statistics(self):
        """记录服务器统计信息"""
        try:
            with QMutexLocker(self.stats_mutex):
                now = datetime.now()
                time_diff = (now - self.stats['last_stats_time']).total_seconds()

                if time_diff > 0:
                    # 计算消息处理速率
                    recent_messages = self.stats['total_messages']
                    self.stats['messages_per_second'] = recent_messages / time_diff if time_diff > 0 else 0
                    self.stats['last_stats_time'] = now

                # 获取队列长度
                api_queue_len = len(self.api_batch_queue)
                update_buffer_len = len(self.update_buffer)
                retry_queue_len = len(self.retry_queue)

                stats_info = (
                    f"服务器统计 | "
                    f"总连接: {self.stats['total_connections']} | "
                    f"活跃连接: {self.stats['active_connections']} | "
                    f"总消息: {self.stats['total_messages']} | "
                    f"失败消息: {self.stats['failed_messages']} | "
                    f"消息/秒: {self.stats['messages_per_second']:.2f} | "
                    f"API队列: {api_queue_len} | "
                    f"UI缓冲: {update_buffer_len} | "
                    f"重试队列: {retry_queue_len}"
                )

                logging.info(stats_info)

                # 检查异常情况
                if api_queue_len > 1000:
                    logging.warning(f"API队列积压严重: {api_queue_len}")
                if update_buffer_len > 500:
                    logging.warning(f"UI更新缓冲区积压: {update_buffer_len}")
                if retry_queue_len > 100:
                    logging.warning(f"重试队列积压: {retry_queue_len}")

        except Exception as e:
            logging.error(f"统计日志异常: {str(e)}")

    def _flush_api_batch(self):
        """批量发送API数据"""
        batch = []
        with QMutexLocker(self.api_batch_mutex):
            if not self.api_batch_queue:
                return
            # 限制批量大小以避免内存问题
            batch_size = min(len(self.api_batch_queue), API_BATCH_SIZE)
            for _ in range(batch_size):
                if self.api_batch_queue:
                    batch.append(self.api_batch_queue.popleft())

        if not batch:
            return

        try:
            logging.info(f"开始批量发送API数据，记录数: {len(batch)}")
            success = self.api_client.send_batch_data(batch)

            # 处理发送结果
            failed_records = []
            for record in batch:
                if success:
                    record.status = "已成功"
                else:
                    record.status = "已失败"
                    record.last_retry = datetime.now()
                    # 将失败的记录加入重试队列
                    if record.retry_count < MAX_RETRY_ATTEMPTS:
                        failed_records.append(record)

                # 更新UI
                with QMutexLocker(self.update_buffer_mutex):
                    self.update_buffer.append(record)

            # 将失败的记录加入重试队列
            if failed_records:
                with QMutexLocker(self.retry_mutex):
                    self.retry_queue.extend(failed_records)

        except Exception as e:
            logging.error(f"批量API发送异常: {str(e)}")
            # 处理异常情况
            failed_records = []
            for record in batch:
                record.status = "已失败"
                record.last_retry = datetime.now()
                if record.retry_count < MAX_RETRY_ATTEMPTS:
                    failed_records.append(record)

                with QMutexLocker(self.update_buffer_mutex):
                    self.update_buffer.append(record)

            # 将失败的记录加入重试队列
            if failed_records:
                with QMutexLocker(self.retry_mutex):
                    self.retry_queue.extend(failed_records)