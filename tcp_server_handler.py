# TCP服务器处理模块：负责TCP服务端的启动、客户端连接管理、消息接收与解析、数据上传和重试机制
# 主要类TcpServerHandler，集成了多线程、信号通信、线程安全等功能
import socket
import logging
from datetime import datetime
from collections import deque
from concurrent.futures import Thread<PERSON>oolExecutor
from PyQt5.QtCore import QObject, pyqtSignal, QTimer, QMutex, QMutexLocker
from data_record import DataRecord
from upload_manager import start_upload_manager, stop_upload_manager
from config import (
    PORT, MESSAGE_LENGTH, MAX_MEMORY_RECORDS,
    MAX_CONNECTIONS, SOCKET_RECV_BUFFER, SOCKET_SEND_BUFFER,
    MAX_CONSECUTIVE_ERRORS, MESSAGE_RECEIVE_TIMEOUT,
    NETWORK_THREADS, DATA_PROCESSING_THREADS, UI_UPDATE_INTERVAL,
    STATS_LOG_INTERVAL, MAX_BUFFER_SIZE, RECV_TIMEOUT
)

class TcpServerHandler(QObject):
    update_signal = pyqtSignal(object)  # 发送DataRecord对象列表或单个对象
    connection_update_signal = pyqtSignal(str)

    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.server_socket = None
        self.running = False
        self.data_records = deque(maxlen=MAX_MEMORY_RECORDS)  # 限制内存中记录数量

        # 上传管理器将在start()方法中初始化
        self.upload_manager = None

        self.unique_chips = set()
        self.client_sockets = []

        self.data_mutex = QMutex()
        self.chip_mutex = QMutex()
        self.conn_mutex = QMutex()

        # 简化线程池配置，专注于数据读取和界面更新
        self.network_executor = ThreadPoolExecutor(max_workers=NETWORK_THREADS, thread_name_prefix="Network")
        self.data_processing_executor = ThreadPoolExecutor(max_workers=DATA_PROCESSING_THREADS, thread_name_prefix="DataProc")

        # UI批量更新机制
        self.update_buffer = []
        self.update_buffer_mutex = QMutex()
        self.update_timer = QTimer()
        self.update_timer.setInterval(UI_UPDATE_INTERVAL)  # UI更新间隔
        self.update_timer.timeout.connect(self._emit_batch_updates)

        # 流量统计和监控
        self.stats = {
            'total_connections': 0,
            'active_connections': 0,
            'total_messages': 0,
            'failed_messages': 0,
            'last_message_time': None,
            'messages_per_second': 0,
            'last_stats_time': datetime.now(),
            'total_bytes_received': 0,
            'last_data_time': None,
            'data_gaps': 0  # 数据间隔次数
        }
        self.stats_mutex = QMutex()

        # 统计定时器
        self.stats_timer = QTimer()
        self.stats_timer.setInterval(STATS_LOG_INTERVAL)  # 统计日志间隔
        self.stats_timer.timeout.connect(self._log_statistics)
        self.stats_timer.start()

        # 空闲检测定时器，处理剩余数据
        self.idle_timer = QTimer()
        self.idle_timer.setInterval(3000)  # 3秒检查一次空闲状态，更快处理剩余数据
        self.idle_timer.timeout.connect(self._check_idle_and_flush)
        self.idle_timer.start()



    def _accept_connections(self):
        """接受客户端连接"""
        while self.running:
            try:
                client_socket, addr = self.server_socket.accept()
                addr_str = f"{addr[0]}:{addr[1]}"
                logging.info(f"客户端连接: {addr_str}")

                # 检查连接数限制，防止资源耗尽
                with QMutexLocker(self.conn_mutex):
                    if len(self.client_sockets) >= MAX_CONNECTIONS:  # 限制最大连接数
                        logging.warning(f"连接数已达上限，拒绝新连接: {addr_str}")
                        try:
                            client_socket.close()
                        except:
                            pass
                        continue

                    self.client_sockets.append(client_socket)

                # 更新连接统计
                with QMutexLocker(self.stats_mutex):
                    self.stats['total_connections'] += 1
                    self.stats['active_connections'] = len(self.client_sockets)

                self.connection_update_signal.emit(f"已连接: {addr_str}")
                logging.info(f"设备连接成功: {addr_str}")

                # 启动线程处理客户端消息
                self.network_executor.submit(
                    self._handle_client,
                    client_socket,
                    addr_str
                )

            except socket.error as e:
                if self.running:
                    logging.error(f"Socket接受连接错误: {str(e)}")
                    # 短暂休眠避免CPU占用过高
                    import time
                    time.sleep(0.1)
            except Exception as e:
                if self.running:
                    logging.error(f"接受连接异常: {str(e)}")
                break

    def start(self):
        """启动TCP服务器"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)

            # 优化服务器socket配置以减少丢包
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)  # 允许地址重用
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, 1024 * 1024)  # 1MB接收缓冲区
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 1024 * 1024)  # 1MB发送缓冲区

            # 设置TCP选项以提高性能
            if hasattr(socket, 'TCP_NODELAY'):
                self.server_socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)  # 禁用Nagle算法

            self.server_socket.bind(('', PORT))
            self.server_socket.listen(128)  # 增加连接队列长度，支持更多并发连接
            self.running = True

            # 启动定时器
            self.update_timer.start()

            # 启动独立的上传管理器（每次启动都创建新实例）
            self.upload_manager = start_upload_manager()

            # 启动连接接受线程
            self.network_executor.submit(self._accept_connections)

            logging.info(f"TCP服务器已启动 - 端口: {PORT}, 连接队列: 128")
        except Exception as e:
            logging.error(f"服务器启动失败: {str(e)}")
            raise

    def _handle_client(self, client_socket, addr_str):
        """处理客户端连接"""
        consecutive_errors = 0
        max_consecutive_errors = MAX_CONSECUTIVE_ERRORS
        buffer = b''  # 用于处理TCP流数据

        try:
            # 优化socket选项以提高性能和可靠性
            client_socket.settimeout(RECV_TIMEOUT)  # 短超时时间，提高响应性
            client_socket.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
            client_socket.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, SOCKET_RECV_BUFFER)  # 接收缓冲区
            client_socket.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, SOCKET_SEND_BUFFER)  # 发送缓冲区

            # 设置TCP选项以减少延迟
            if hasattr(socket, 'TCP_NODELAY'):
                client_socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
            if hasattr(socket, 'SO_KEEPALIVE'):
                client_socket.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)

            # 设置更激进的TCP选项
            try:
                if hasattr(socket, 'TCP_QUICKACK'):
                    client_socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_QUICKACK, 1)
            except:
                pass

            logging.info(f"客户端连接已优化: {addr_str}")

            # 连续超时计数器
            timeout_count = 0
            max_timeouts = 30  # 允许30次连续超时 (30秒)

            while self.running:
                try:
                    # 使用较小的接收缓冲区以提高响应性
                    data = client_socket.recv(1024)  # 减小到1024字节
                    if not data:
                        logging.info(f"客户端正常断开: {addr_str}")
                        break

                    # 重置超时计数器
                    timeout_count = 0
                    consecutive_errors = 0

                    # 更新数据接收统计
                    now = datetime.now()
                    with QMutexLocker(self.stats_mutex):
                        self.stats['total_bytes_received'] += len(data)
                        if self.stats['last_data_time']:
                            gap = (now - self.stats['last_data_time']).total_seconds()
                            if gap > 2.0:  # 超过2秒的间隔认为是数据间隔
                                self.stats['data_gaps'] += 1
                                logging.debug(f"数据间隔 {gap:.1f}秒 [{addr_str}]")
                        self.stats['last_data_time'] = now

                    buffer += data

                    # 从缓冲区中提取完整消息
                    messages = self._extract_messages_from_buffer(buffer)
                    if messages is None:
                        # 缓冲区数据不足，继续接收
                        continue

                    buffer, extracted_messages = messages

                    # 处理提取的消息
                    for message in extracted_messages:
                        self.data_processing_executor.submit(self._process_message, message, addr_str)

                except socket.timeout:
                    timeout_count += 1
                    # 只在调试模式下记录超时
                    if timeout_count % 10 == 0:  # 每10次超时记录一次
                        logging.debug(f"客户端接收超时 ({timeout_count}/{max_timeouts}): {addr_str}")

                    if timeout_count >= max_timeouts:
                        logging.warning(f"客户端长时间无数据，断开连接: {addr_str}")
                        break
                    continue

                except socket.error as e:
                    consecutive_errors += 1
                    logging.error(f"Socket错误 ({consecutive_errors}/{max_consecutive_errors}): {addr_str} - {str(e)}")
                    if consecutive_errors >= max_consecutive_errors:
                        logging.error(f"客户端连续错误次数过多，断开连接: {addr_str}")
                        break

                    # 短暂休眠后重试
                    import time
                    time.sleep(0.05)  # 减少到50ms

        except Exception as e:
            logging.error(f"客户端处理异常: {addr_str} - {str(e)}")
        finally:
            self._cleanup_client_connection(client_socket, addr_str)

    def _extract_messages_from_buffer(self, buffer):
        """从缓冲区中提取完整的消息"""
        try:
            messages = []
            remaining_buffer = buffer

            while len(remaining_buffer) >= MESSAGE_LENGTH:
                # 寻找消息开始标志 '#'
                start_pos = -1
                for i in range(len(remaining_buffer) - MESSAGE_LENGTH + 1):
                    if remaining_buffer[i:i+1] == b'#':
                        # 检查这个位置是否能构成完整消息
                        if i + MESSAGE_LENGTH <= len(remaining_buffer):
                            potential_message = remaining_buffer[i:i+MESSAGE_LENGTH]
                            try:
                                decoded = potential_message.decode('utf-8', errors='replace')
                                if self._is_valid_message_format(decoded):
                                    start_pos = i
                                    break
                            except:
                                continue

                if start_pos == -1:
                    # 没有找到有效的消息开始位置
                    if len(remaining_buffer) > MAX_BUFFER_SIZE:
                        # 如果缓冲区太大，丢弃一些数据避免内存问题
                        logging.warning(f"缓冲区过大，丢弃部分数据: {len(remaining_buffer)} 字节")
                        remaining_buffer = remaining_buffer[-MESSAGE_LENGTH:]
                    break

                # 如果开始位置不是0，说明前面有无效数据
                if start_pos > 0:
                    invalid_data = remaining_buffer[:start_pos]
                    logging.warning(f"丢弃无效数据 ({len(invalid_data)} 字节): {invalid_data[:20]}{'...' if len(invalid_data) > 20 else ''}")
                    remaining_buffer = remaining_buffer[start_pos:]

                # 提取消息
                message_bytes = remaining_buffer[:MESSAGE_LENGTH]
                message = message_bytes.decode('utf-8', errors='replace')

                if self._is_valid_message_format(message):
                    messages.append(message)
                    remaining_buffer = remaining_buffer[MESSAGE_LENGTH:]
                else:
                    # 消息格式无效，跳过这个字节继续寻找
                    remaining_buffer = remaining_buffer[1:]

            return remaining_buffer, messages

        except Exception as e:
            logging.error(f"提取消息异常: {str(e)}")
            return buffer, []

    def _is_valid_message_format(self, message):
        """验证消息格式是否有效"""
        try:
            if len(message) != MESSAGE_LENGTH:
                return False
            if not message.startswith('#'):
                return False
            # 可以添加更多格式验证
            return True
        except:
            return False

    def _receive_complete_message(self, sock):
        """接收完整的消息，确保数据完整性"""
        try:
            msg = b''
            start_time = datetime.now()
            max_receive_time = MESSAGE_RECEIVE_TIMEOUT  # 最大接收时间

            while len(msg) < MESSAGE_LENGTH:
                # 检查接收超时
                if (datetime.now() - start_time).total_seconds() > max_receive_time:
                    logging.warning(f"消息接收超时，已接收: {len(msg)}/{MESSAGE_LENGTH} 字节")
                    return None

                # 计算还需要接收的字节数
                remaining = MESSAGE_LENGTH - len(msg)

                # 使用较小的块大小以提高响应性
                chunk_size = min(remaining, 1024)
                chunk = sock.recv(chunk_size)

                if not chunk:
                    # 连接已关闭
                    if len(msg) > 0:
                        logging.warning(f"连接意外关闭，已接收: {len(msg)}/{MESSAGE_LENGTH} 字节")
                    return None

                msg += chunk

            # 验证消息长度
            if len(msg) != MESSAGE_LENGTH:
                logging.error(f"消息长度不匹配: 期望{MESSAGE_LENGTH}, 实际{len(msg)}")
                return None

            # 解码并返回完整消息
            decoded_msg = msg.decode('utf-8', errors='replace')

            # 基本格式验证
            if not decoded_msg.startswith('#'):
                logging.warning(f"消息格式异常，不以#开头: {decoded_msg[:10]}...")
                return None

            return decoded_msg

        except socket.timeout:
            raise  # 重新抛出超时异常
        except socket.error:
            raise  # 重新抛出socket错误
        except Exception as e:
            logging.error(f"接收消息异常: {str(e)}")
            return None

    def _cleanup_client_connection(self, client_socket, addr_str):
        """清理客户端连接资源"""
        try:
            with QMutexLocker(self.conn_mutex):
                if client_socket in self.client_sockets:
                    self.client_sockets.remove(client_socket)

            # 更新连接统计
            with QMutexLocker(self.stats_mutex):
                self.stats['active_connections'] = len(self.client_sockets)

            try:
                client_socket.shutdown(socket.SHUT_RDWR)
            except:
                pass
            client_socket.close()

            logging.info(f"客户端断开: {addr_str}")
            self.connection_update_signal.emit(f"已断开: {addr_str}")
        except Exception as e:
            logging.error(f"清理客户端连接异常: {str(e)}")

    def _process_message(self, message, addr_str):
        """处理消息内容"""
        # 更新消息统计
        with QMutexLocker(self.stats_mutex):
            self.stats['total_messages'] += 1
            self.stats['last_message_time'] = datetime.now()

        # 验证消息格式和长度（这里应该已经通过了_is_valid_message_format验证）
        logging.debug(f"处理消息 [{addr_str}]: {message[:20]}... (长度: {len(message)})")

        if not self._is_valid_message_format(message):
            logging.warning(f"消息格式验证失败 [{addr_str}]: {message[:20]}...")
            # 更新失败统计
            with QMutexLocker(self.stats_mutex):
                self.stats['failed_messages'] += 1
            return

        try:
            # 解析数据
            # 解析数据
            # 原始消息格式: #FC0011112506192129040000000000A00000086
            # 设备编号: FC001111 (从索引1开始，长度8)
            # EPC代码: (从索引25开始，长度6)
            device_number = message[1:9]  # 设备编号
            card_number = message[25:31]  # EPC代码
            now_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]  # 含毫秒的时间

            # 创建记录
            record = DataRecord(card_number, device_number, now_time)

            # 立即处理界面更新（高优先级，不阻塞）
            self._add_to_update_buffer_immediate(record)

            # 异步处理数据存储和统计
            self.data_processing_executor.submit(self._process_data_storage, record)

            # 异步提交到上传管理器（完全独立）
            self.data_processing_executor.submit(self._submit_to_upload_manager, record)
        except Exception as e:
            logging.error(f"消息处理异常: {str(e)}")

    def _add_to_update_buffer_immediate(self, record):
        """立即将记录添加到UI更新缓冲区（非阻塞）"""
        with QMutexLocker(self.update_buffer_mutex):
            self.update_buffer.append(record)

            # 如果缓冲区达到一定数量，立即发送更新信号
            if len(self.update_buffer) >= 30:  # 每30条记录更新，平衡效率和响应性
                self._emit_batch_updates()

    def _process_data_storage(self, record):
        """异步处理数据存储和统计（不影响界面）"""
        try:
            # 将记录添加到内存记录
            with QMutexLocker(self.data_mutex):
                self.data_records.append(record)

            # 更新芯片统计
            with QMutexLocker(self.chip_mutex):
                if record.chip_id not in self.unique_chips:
                    self.unique_chips.add(record.chip_id)

            logging.debug(f"数据存储成功 | 设备: {record.device_id} | 芯片: {record.chip_id}")

        except Exception as e:
            logging.error(f"数据存储异常: {str(e)}")

    def _submit_to_upload_manager(self, record):
        """提交记录到独立的上传管理器（完全不阻塞）"""
        try:
            # 检查上传管理器是否可用
            if self.upload_manager:
                # 异步提交到上传管理器，不等待结果
                self.upload_manager.add_record(record)
                logging.debug(f"已提交上传 | 芯片: {record.chip_id}")
            else:
                logging.warning(f"上传管理器未启动，跳过上传 | 芯片: {record.chip_id}")

        except Exception as e:
            logging.error(f"提交上传异常: {str(e)}")



    def _emit_batch_updates(self):
        """批量发送UI更新信号"""
        try:
            with QMutexLocker(self.update_buffer_mutex):
                if self.update_buffer:
                    # 发送批量更新信号，使用列表传递多个记录
                    self.update_signal.emit(self.update_buffer.copy())
                    logging.debug(f"发送UI更新: {len(self.update_buffer)} 条记录")
                    self.update_buffer.clear()
        except KeyboardInterrupt:
            logging.info("收到中断信号，停止批量更新")
            raise
        except Exception as e:
            logging.error(f"批量更新异常: {str(e)}")

    def _check_idle_and_flush(self):
        """检查空闲状态并刷新剩余数据"""
        if not self.running:
            return

        try:

            # 检查是否有剩余的UI更新数据
            ui_buffer_size = 0
            with QMutexLocker(self.update_buffer_mutex):
                ui_buffer_size = len(self.update_buffer)
                if self.update_buffer:
                    logging.info(f"空闲检测: 发送剩余的 {len(self.update_buffer)} 条UI更新")
                    self.update_signal.emit(self.update_buffer.copy())
                    self.update_buffer.clear()

            # 通知上传管理器检查剩余数据
            if self.upload_manager:
                # 使用统一的剩余数据处理方法
                self.upload_manager.flush_remaining_data()

            # 如果没有剩余数据，记录一次调试信息
            if ui_buffer_size == 0:
                upload_stats = self.upload_manager.get_stats() if self.upload_manager else {}
                queue_size = upload_stats.get('queue_size', 0)
                if queue_size == 0:
                    logging.debug("空闲检测: 没有剩余数据需要处理")

        except Exception as e:
            logging.error(f"空闲检测异常: {str(e)}")

    def stop(self):
        """停止服务器"""
        logging.info("开始停止服务器...")
        self.running = False

        # 发送剩余的缓冲区数据
        self._emit_batch_updates()
        logging.info("已发送剩余的UI更新数据")

        # 停止定时器
        self.update_timer.stop()
        self.stats_timer.stop()
        self.idle_timer.stop()

        # 停止独立的上传管理器
        if self.upload_manager:
            # 先检查剩余数据
            upload_stats = self.upload_manager.get_stats()
            if upload_stats.get('queue_size', 0) > 0:
                logging.info(f"停止前还有 {upload_stats['queue_size']} 条数据待上传")

            stop_upload_manager()
            self.upload_manager = None

        # 关闭客户端连接
        with QMutexLocker(self.conn_mutex):
            logging.info(f"关闭 {len(self.client_sockets)} 个客户端连接")
            for sock in self.client_sockets:
                try:
                    sock.shutdown(socket.SHUT_RDWR)
                    sock.close()
                except Exception:
                    pass
            self.client_sockets.clear()

        # 关闭服务器socket
        if self.server_socket:
            try:
                self.server_socket.close()
                logging.info("服务器socket已关闭")
            except Exception:
                pass

        # 关闭线程池
        logging.info("关闭线程池...")
        executors = [
            ("Network", self.network_executor),
            ("DataProcessing", self.data_processing_executor)
        ]

        for name, executor in executors:
            try:
                executor.shutdown(wait=False)
                logging.info(f"{name} 线程池已关闭")
            except Exception as e:
                logging.error(f"关闭 {name} 线程池异常: {str(e)}")

        logging.info("服务器已完全停止")

    def _log_statistics(self):
        """记录服务器统计信息"""
        try:
            with QMutexLocker(self.stats_mutex):
                now = datetime.now()
                time_diff = (now - self.stats['last_stats_time']).total_seconds()

                if time_diff > 0:
                    # 计算消息处理速率
                    recent_messages = self.stats['total_messages']
                    self.stats['messages_per_second'] = recent_messages / time_diff if time_diff > 0 else 0
                    self.stats['last_stats_time'] = now

                # 获取队列长度
                if self.upload_manager:
                    upload_stats = self.upload_manager.get_stats()
                else:
                    upload_stats = {'queue_size': 0, 'retry_size': 0, 'total_uploaded': 0, 'total_failed': 0}
                update_buffer_len = len(self.update_buffer)

                # 计算数据接收速率
                bytes_mb = self.stats['total_bytes_received'] / (1024 * 1024)

                stats_info = (
                    f"服务器统计 | "
                    f"总连接: {self.stats['total_connections']} | "
                    f"活跃连接: {self.stats['active_connections']} | "
                    f"总消息: {self.stats['total_messages']} | "
                    f"失败消息: {self.stats['failed_messages']} | "
                    f"消息/秒: {self.stats['messages_per_second']:.2f} | "
                    f"接收数据: {bytes_mb:.2f}MB | "
                    f"数据间隔: {self.stats['data_gaps']} | "
                    f"上传队列: {upload_stats['queue_size']} | "
                    f"重试队列: {upload_stats['retry_size']} | "
                    f"已上传: {upload_stats['total_uploaded']} | "
                    f"上传失败: {upload_stats['total_failed']} | "
                    f"UI缓冲: {update_buffer_len}"
                )

                logging.info(stats_info)

                # 检查异常情况
                if upload_stats['queue_size'] > 1000:
                    logging.warning(f"上传队列积压严重: {upload_stats['queue_size']}")
                if update_buffer_len > 500:
                    logging.warning(f"UI更新缓冲区积压: {update_buffer_len}")
                if upload_stats['retry_size'] > 100:
                    logging.warning(f"重试队列积压: {upload_stats['retry_size']}")

        except Exception as e:
            logging.error(f"统计日志异常: {str(e)}")



