# TCP服务器处理模块：负责TCP服务端的启动、客户端连接管理、消息接收与解析、数据上传和重试机制
# 主要类TcpServerHandler，集成了多线程、信号通信、线程安全等功能
import socket
import threading
import logging
from datetime import datetime
from collections import deque
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
from PyQt5.QtCore import QObject, pyqtSignal, QTimer, QMutex, QMutexLocker
import sqlite3
import os
from data_record import DataRecord
from api_client import APIClient
from config import PORT, MESSAGE_LENGTH, RETRY_INTERVAL, DB_PATH, MAX_MEMORY_RECORDS
from config import MAX_RETRY_ATTEMPTS

class TcpServerHandler(QObject):
    update_signal = pyqtSignal(object)  # 发送DataRecord对象列表或单个对象
    connection_update_signal = pyqtSignal(str)

    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.server_socket = None
        self.running = False
        self.data_records = deque(maxlen=MAX_MEMORY_RECORDS)  # 限制内存中记录数量
        self.retry_queue = deque()  # 单独的重试队列
        self.api_client = APIClient() # 初始化API客户端

        self.unique_chips = set()
        self.client_sockets = []

        self.data_mutex = QMutex()
        self.retry_mutex = QMutex()  # 重试队列的互斥锁
        self.chip_mutex = QMutex()
        self.conn_mutex = QMutex()

        self.network_executor = ThreadPoolExecutor(max_workers=10)
        self.data_processing_executor = ThreadPoolExecutor(max_workers=10) # 新增数据处理线程池，优化为10线程
        self.retry_executor = ThreadPoolExecutor(max_workers=10)
        self.api_executor = ThreadPoolExecutor(max_workers=20)

        self.retry_timer = QTimer()
        self.retry_timer.timeout.connect(self.process_retry)

        # UI批量更新机制
        self.update_buffer = [] 
        self.update_buffer_mutex = QMutex()
        self.update_timer = QTimer()
        self.update_timer.setInterval(100)  # 每100ms批量更新一次UI
        self.update_timer.timeout.connect(self._emit_batch_updates)

        # 批量API上传队列和定时器
        self.api_batch_queue = deque()
        self.api_batch_mutex = QMutex()
        self.api_batch_timer = QTimer()
        self.api_batch_timer.setInterval(5000)  # 5秒
        self.api_batch_timer.timeout.connect(self._flush_api_batch)
        self.api_batch_timer.start()

    def _accept_connections(self):
        """接受客户端连接"""
        while self.running:
            try:
                client_socket, addr = self.server_socket.accept()
                addr_str = f"{addr[0]}:{addr[1]}"
                logging.info(f"客户端连接: {addr_str}")
                
                with QMutexLocker(self.conn_mutex):
                    self.client_sockets.append(client_socket)
                
                self.connection_update_signal.emit(f"已连接: {addr_str}")
                
                # 启动线程处理客户端消息
                self.network_executor.submit(
                    self._handle_client,
                    client_socket,
                    addr_str
                )
            except Exception as e:
                if self.running:
                    logging.error(f"接受连接异常: {str(e)}")
                break

    def start(self):
        """启动TCP服务器"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.bind(('', PORT))
            self.server_socket.listen(5)
            self.running = True
            self.retry_timer.start(RETRY_INTERVAL * 1000)
            self.update_timer.start()
            
            # 启动连接接受线程
            self.network_executor.submit(self._accept_connections)
            
            logging.info("TCP服务器已启动")
        except Exception as e:
            logging.error(f"服务器启动失败: {str(e)}")
            raise

    def _handle_client(self, client_socket, addr_str):
        """处理客户端连接"""
        try:
            while self.running:
                data = client_socket.recv(1024)
                if not data:
                    break
                message = data.decode('utf-8').strip()
                self._process_message(message, addr_str)
        except Exception as e:
            logging.error(f"客户端处理异常: {str(e)}")
        finally:
            with QMutexLocker(self.conn_mutex):
                if client_socket in self.client_sockets:
                    self.client_sockets.remove(client_socket)
            client_socket.close()
            logging.info(f"客户端断开: {addr_str}")
            self.connection_update_signal.emit(f"已断开: {addr_str}")

    def _process_message(self, message, addr_str):
        """处理消息内容"""
        # 验证消息格式和长度
        logging.debug(f"接收到消息: {message}, 长度: {len(message)}, 第一个字符: {message[0] if message else 'N/A'}")
        if not message.startswith('#') or len(message) != MESSAGE_LENGTH:
            logging.warning(f"无效消息格式或长度: {message} (期望长度: {MESSAGE_LENGTH}, 实际长度: {len(message)}, 是否以#开头: {message.startswith('#')})")
            return

        try:
            # 解析数据
            # 解析数据
            # 原始消息格式: #FC0011112506192129040000000000A00000086
            # 设备编号: FC001111 (从索引1开始，长度8)
            # EPC代码: (从索引25开始，长度6)
            device_number = message[1:9]  # 设备编号
            card_number = message[25:31]  # EPC代码
            now_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]  # 含毫秒的时间

            # 创建记录
            record = DataRecord(card_number, device_number, now_time)

            # 异步处理数据保存、API发送和添加到内存记录
            self.data_processing_executor.submit(self._process_data_async, record)

            # 批量更新UI (异步处理)
            self.data_processing_executor.submit(self._add_to_update_buffer_async, record)
        except Exception as e:
            logging.error(f"消息处理异常: {str(e)}")

    def _process_data_async(self, record):
        """异步处理数据和API发送"""
        # 将记录添加到内存记录
        with QMutexLocker(self.data_mutex):
            self.data_records.append(record)

        # 直接提交API批量队列
        self.api_executor.submit(self._send_to_api, record)

        logging.info(f"处理消息成功 | 设备: {record.device_id} | 芯片: {record.chip_id} | 时间: {record.scan_time}")

        # 更新芯片统计
        with QMutexLocker(self.chip_mutex):
            if record.chip_id not in self.unique_chips:
                self.unique_chips.add(record.chip_id)

    def _add_to_update_buffer_async(self, record):
        """异步将记录添加到UI更新缓冲区"""
        with QMutexLocker(self.update_buffer_mutex):
            self.update_buffer.append(record)

    def _send_to_api(self, record):
        """异步发送数据到API（批量上传队列）"""
        with QMutexLocker(self.api_batch_mutex):
            self.api_batch_queue.append(record)

    def process_retry(self):
        while self.retry_queue:
            record = self.retry_queue.popleft()
            logging.debug(f"DEBUG: Inside process_retry, type of record: {type(record)}")
            if not isinstance(record, DataRecord):
                logging.error(f"ERROR: process_retry received non-DataRecord object: {type(record)}")
                continue

            if record.retry_count < MAX_RETRY_ATTEMPTS:
                record.retry_count += 1
                record.last_retry = datetime.now()
                logging.info(f"重试上传: {record.chip_id}, 第 {record.retry_count} 次")
                self.api_executor.submit(self._send_to_api, record)
            else:
                record.status = "重试失败"
                with QMutexLocker(self.update_buffer_mutex):
                    self.update_buffer.append(record)

    def _emit_batch_updates(self):
        #""批量发送UI更新信号""
        with QMutexLocker(self.update_buffer_mutex):
            if self.update_buffer:
                # 发送批量更新信号，使用列表传递多个记录
                self.update_signal.emit(self.update_buffer.copy())
                self.update_buffer.clear()

    def stop(self):
        #""停止服务器""
        self.running = False
        self.retry_timer.stop()
        self.update_timer.stop()

        # 关闭客户端连接
        with QMutexLocker(self.conn_mutex):
            for sock in self.client_sockets:
                try:
                    sock.shutdown(socket.SHUT_RDWR)
                    sock.close()
                except Exception as e:
                    pass
            self.client_sockets.clear()

        # 关闭服务器socket
        try:
            self.server_socket.close()
        except Exception as e:
            pass

        # 关闭线程池
        self.network_executor.shutdown(wait=False)
        self.api_executor.shutdown(wait=False)
        logging.info("服务器已停止")

    def _flush_api_batch(self):
        with QMutexLocker(self.api_batch_mutex):
            if not self.api_batch_queue:
                return
            batch = []
            while self.api_batch_queue:
                batch.append(self.api_batch_queue.popleft())
        if not batch:
            return
        try:
            success = self.api_client.send_batch_data(batch)
            for record in batch:
                record.status = "已成功" if success else "已失败"
                with QMutexLocker(self.update_buffer_mutex):
                    self.update_buffer.append(record)
        except Exception as e:
            logging.error(f"批量API发送异常: {str(e)}")
            for record in batch:
                record.status = "已失败"
                with QMutexLocker(self.update_buffer_mutex):
                    self.update_buffer.append(record)