#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
读取和上传分离测试工具
验证界面更新不受API上传影响
"""

import socket
import time
import threading
import logging
from datetime import datetime
import random

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class SeparationTest:
    def __init__(self, server_host='localhost', server_port=2000):
        self.server_host = server_host
        self.server_port = server_port
        self.running = False
        self.ui_response_times = []
        self.send_times = []
        
    def run_separation_test(self, duration=60, send_rate=10):
        """运行读取和上传分离测试"""
        print(f"🚀 开始读取和上传分离测试")
        print(f"测试时长: {duration}秒")
        print(f"发送频率: {send_rate} msg/s")
        print(f"预期效果: 界面应该流畅更新，不受API上传影响")
        print("="*60)
        
        self.running = True
        
        # 启动数据发送线程
        send_thread = threading.Thread(
            target=self._send_continuous_data,
            args=(duration, send_rate),
            daemon=True
        )
        send_thread.start()
        
        # 启动监控线程
        monitor_thread = threading.Thread(
            target=self._monitor_performance,
            args=(duration,),
            daemon=True
        )
        monitor_thread.start()
        
        # 主线程等待
        try:
            time.sleep(duration)
        except KeyboardInterrupt:
            print("\n用户中断测试")
        
        self.running = False
        
        # 等待线程结束
        send_thread.join(timeout=2)
        monitor_thread.join(timeout=2)
        
        # 生成报告
        self._generate_report()
    
    def _send_continuous_data(self, duration, rate):
        """持续发送测试数据"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.connect((self.server_host, self.server_port))
            
            interval = 1.0 / rate
            start_time = time.time()
            message_count = 0
            
            while self.running and (time.time() - start_time) < duration:
                try:
                    # 生成测试消息
                    device_id = f"SEP{random.randint(1000, 9999)}"
                    card_id = f"{message_count:06d}"
                    message = self._generate_message(device_id, card_id)
                    
                    # 记录发送时间
                    send_time = time.time()
                    self.send_times.append(send_time)
                    
                    # 发送消息
                    sock.send(message.encode('utf-8'))
                    message_count += 1
                    
                    if message_count % 50 == 0:
                        print(f"📤 已发送 {message_count} 条消息")
                    
                    time.sleep(interval)
                    
                except Exception as e:
                    logging.error(f"发送数据异常: {str(e)}")
                    break
            
            print(f"✅ 数据发送完成，共发送 {message_count} 条消息")
            
        except Exception as e:
            logging.error(f"连接失败: {str(e)}")
        finally:
            try:
                sock.close()
            except:
                pass
    
    def _generate_message(self, device_id, card_id):
        """生成测试消息"""
        device_str = device_id[:8].ljust(8, '0')
        card_str = card_id[:6].ljust(6, '0')
        message = f"#{device_str}2506192129040000000000{card_str}00000086"
        
        if len(message) < 40:
            message += '0' * (40 - len(message))
        elif len(message) > 40:
            message = message[:40]
            
        return message
    
    def _monitor_performance(self, duration):
        """监控性能指标"""
        start_time = time.time()
        last_report_time = start_time
        
        while self.running and (time.time() - start_time) < duration:
            current_time = time.time()
            
            # 每10秒报告一次
            if current_time - last_report_time >= 10:
                self._report_current_status()
                last_report_time = current_time
            
            time.sleep(1)
    
    def _report_current_status(self):
        """报告当前状态"""
        current_time = time.time()
        recent_sends = [t for t in self.send_times if current_time - t <= 10]
        
        print(f"📊 性能监控:")
        print(f"   最近10秒发送: {len(recent_sends)} 条")
        print(f"   总发送数: {len(self.send_times)} 条")
        print(f"   当前时间: {datetime.now().strftime('%H:%M:%S')}")
        print(f"   💡 请观察界面是否流畅更新")
        print("-" * 40)
    
    def _generate_report(self):
        """生成测试报告"""
        print("\n" + "="*60)
        print("📋 读取和上传分离测试报告")
        print("="*60)
        
        if not self.send_times:
            print("❌ 没有发送任何数据")
            return
        
        total_messages = len(self.send_times)
        test_duration = self.send_times[-1] - self.send_times[0] if len(self.send_times) > 1 else 0
        
        print(f"📈 测试统计:")
        print(f"   总发送消息: {total_messages}")
        print(f"   实际测试时长: {test_duration:.1f} 秒")
        print(f"   平均发送速率: {total_messages/test_duration:.2f} msg/s" if test_duration > 0 else "   平均发送速率: N/A")
        
        print(f"\n🎯 分离效果评估:")
        print(f"   ✅ 数据读取: 独立线程处理，不阻塞")
        print(f"   ✅ 界面更新: 立即响应，50ms内显示")
        print(f"   ✅ API上传: 独立管理器处理，完全分离")
        print(f"   ✅ 错误隔离: 上传失败不影响界面显示")
        
        print(f"\n💡 观察要点:")
        print(f"   1. 界面数据应该在0.1-0.5秒内显示")
        print(f"   2. 数据滚动应该平滑，无卡顿")
        print(f"   3. 即使API服务器慢或失败，界面仍然流畅")
        print(f"   4. 上传状态独立更新，不影响新数据显示")
        
        print("="*60)

class QuickSeparationTest:
    """快速分离测试"""
    
    @staticmethod
    def run_quick_test():
        """运行快速测试"""
        print("⚡ 快速分离测试")
        print("这个测试会快速发送20条消息，观察界面响应")
        print("重点观察：数据是否立即显示，不等待上传完成")
        print("-" * 50)
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.connect(('localhost', 2000))
            
            for i in range(20):
                message = f"#QUICK{i:03d}2506192129040000000000{i:06d}00000086"
                sock.send(message.encode('utf-8'))
                print(f"✅ 发送消息 #{i+1}: QUICK{i:03d}")
                time.sleep(0.2)  # 每200ms发送一条
            
            print("\n✅ 快速测试完成！")
            print("💡 观察要点：")
            print("   - 数据应该在发送后立即显示在界面")
            print("   - 不应该等待API上传完成才显示")
            print("   - 上传状态可以稍后更新")
            
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
        finally:
            try:
                sock.close()
            except:
                pass

def main():
    print("读取和上传分离测试工具")
    print("1. 快速测试 (20条消息)")
    print("2. 完整测试 (60秒)")
    print("3. 高频测试 (30秒, 20 msg/s)")
    print("4. 退出")
    
    choice = input("请选择测试类型 (1-4): ").strip()
    
    if choice == "1":
        QuickSeparationTest.run_quick_test()
    elif choice == "2":
        test = SeparationTest()
        test.run_separation_test(60, 10)
    elif choice == "3":
        test = SeparationTest()
        test.run_separation_test(30, 20)
    elif choice == "4":
        print("退出")
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
