#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
紧急UI修复工具
用于诊断和修复界面死锁问题
"""

import sys
import logging
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QTextEdit, QPushButton
from PyQt5.QtCore import QTimer, QThread, pyqtSignal
import socket
import time

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class SimpleDataReceiver(QThread):
    """简化的数据接收线程"""
    data_received = pyqtSignal(str)
    
    def __init__(self, host='localhost', port=2000):
        super().__init__()
        self.host = host
        self.port = port
        self.running = False
        
    def run(self):
        """运行数据接收"""
        self.running = True
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.connect((self.host, self.port))
            sock.settimeout(1.0)
            
            buffer = b''
            while self.running:
                try:
                    data = sock.recv(1024)
                    if not data:
                        break
                    
                    buffer += data
                    
                    # 简单的消息提取
                    while len(buffer) >= 40:
                        if buffer[0:1] == b'#':
                            message = buffer[:40].decode('utf-8', errors='replace')
                            buffer = buffer[40:]
                            self.data_received.emit(message)
                        else:
                            buffer = buffer[1:]
                            
                except socket.timeout:
                    continue
                except Exception as e:
                    logging.error(f"接收数据异常: {str(e)}")
                    break
                    
        except Exception as e:
            logging.error(f"连接失败: {str(e)}")
        finally:
            try:
                sock.close()
            except:
                pass
    
    def stop(self):
        """停止接收"""
        self.running = False

class EmergencyUI(QMainWindow):
    """紧急简化UI"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("紧急UI修复测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建文本显示区域
        self.text_display = QTextEdit()
        self.text_display.setMaximumBlockCount(1000)  # 限制行数
        layout.addWidget(self.text_display)
        
        # 创建控制按钮
        self.start_btn = QPushButton("开始测试")
        self.start_btn.clicked.connect(self.toggle_test)
        layout.addWidget(self.start_btn)
        
        # 数据接收器
        self.receiver = None
        self.message_count = 0
        
        # 状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(1000)  # 每秒更新一次状态
        
        logging.info("紧急UI初始化完成")
    
    def toggle_test(self):
        """切换测试状态"""
        if self.receiver is None or not self.receiver.running:
            self.start_test()
        else:
            self.stop_test()
    
    def start_test(self):
        """开始测试"""
        try:
            self.receiver = SimpleDataReceiver()
            self.receiver.data_received.connect(self.handle_data, type=5)  # Qt.QueuedConnection
            self.receiver.start()
            
            self.start_btn.setText("停止测试")
            self.text_display.append("🚀 开始接收数据...")
            logging.info("开始数据接收测试")
            
        except Exception as e:
            self.text_display.append(f"❌ 启动失败: {str(e)}")
            logging.error(f"启动测试失败: {str(e)}")
    
    def stop_test(self):
        """停止测试"""
        if self.receiver:
            self.receiver.stop()
            self.receiver.wait(2000)  # 等待2秒
            self.receiver = None
        
        self.start_btn.setText("开始测试")
        self.text_display.append("⏹️ 停止接收数据")
        logging.info("停止数据接收测试")
    
    def handle_data(self, message):
        """处理接收到的数据"""
        try:
            self.message_count += 1
            
            # 解析消息
            if len(message) >= 40 and message.startswith('#'):
                device_id = message[1:9]
                card_id = message[25:31]
                
                # 简单显示，避免复杂操作
                display_text = f"#{self.message_count:04d} | 设备: {device_id} | 卡片: {card_id}"
                self.text_display.append(display_text)
                
                # 限制显示行数，避免内存问题
                if self.message_count % 100 == 0:
                    self.text_display.clear()
                    self.text_display.append(f"已清理显示，继续接收... (总计: {self.message_count})")
                    
        except Exception as e:
            logging.error(f"处理数据异常: {str(e)}")
    
    def update_status(self):
        """更新状态信息"""
        try:
            status = "运行中" if (self.receiver and self.receiver.running) else "已停止"
            self.setWindowTitle(f"紧急UI修复测试 - {status} - 消息数: {self.message_count}")
        except Exception as e:
            logging.error(f"更新状态异常: {str(e)}")
    
    def closeEvent(self, event):
        """关闭事件"""
        self.stop_test()
        event.accept()

def run_emergency_test():
    """运行紧急测试"""
    print("🚨 紧急UI修复测试")
    print("这个工具用于测试简化的UI是否能正常工作")
    print("如果这个界面也死掉，说明问题可能在TCP服务器端")
    print("-" * 50)
    
    app = QApplication(sys.argv)
    
    # 创建紧急UI
    emergency_ui = EmergencyUI()
    emergency_ui.show()
    
    # 运行应用
    try:
        sys.exit(app.exec_())
    except KeyboardInterrupt:
        print("\n用户中断")

def run_connection_test():
    """运行连接测试"""
    print("🔌 TCP连接测试")
    print("测试是否能正常连接到服务器")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5.0)
        sock.connect(('localhost', 2000))
        
        print("✅ 连接成功")
        
        # 发送测试消息
        test_message = "#TEST0001250619212904000000000000100000086"
        sock.send(test_message.encode('utf-8'))
        print("✅ 发送测试消息成功")
        
        # 尝试接收数据
        sock.settimeout(2.0)
        data = sock.recv(1024)
        if data:
            print(f"⚠️  服务器返回了数据: {data}")
        else:
            print("✅ 服务器正常，无返回数据")
            
    except socket.timeout:
        print("⏰ 连接超时 - 服务器可能未启动")
    except ConnectionRefusedError:
        print("❌ 连接被拒绝 - 服务器未启动")
    except Exception as e:
        print(f"❌ 连接失败: {str(e)}")
    finally:
        try:
            sock.close()
        except:
            pass

def main():
    print("紧急UI修复工具")
    print("1. 运行简化UI测试")
    print("2. 运行TCP连接测试")
    print("3. 退出")
    
    choice = input("请选择操作 (1-3): ").strip()
    
    if choice == "1":
        run_emergency_test()
    elif choice == "2":
        run_connection_test()
    elif choice == "3":
        print("退出")
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
