#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TCP服务器性能测试脚本
用于测试优化后的TCP服务器处理能力和丢包情况
"""

import socket
import threading
import time
import logging
from datetime import datetime
import random

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class TCPTestClient:
    def __init__(self, server_host='localhost', server_port=2000):
        self.server_host = server_host
        self.server_port = server_port
        self.sent_count = 0
        self.error_count = 0
        self.running = False
        
    def generate_test_message(self, device_id, card_id):
        """生成测试消息"""
        # 消息格式: #FC0011112506192129040000000000A00000086
        # 设备编号: FC001111 (从索引1开始，长度8)
        # EPC代码: (从索引25开始，长度6)
        
        device_str = f"{device_id:08d}"[:8]  # 8位设备编号
        card_str = f"{card_id:06d}"[:6]      # 6位卡号
        
        # 构造40字节消息
        message = f"#{device_str}2506192129040000000000{card_str}00000086"
        
        # 确保消息长度为40
        if len(message) < 40:
            message += '0' * (40 - len(message))
        elif len(message) > 40:
            message = message[:40]
            
        return message
    
    def send_messages(self, num_messages=1000, delay=0.01):
        """发送测试消息"""
        self.running = True
        self.sent_count = 0
        self.error_count = 0
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.connect((self.server_host, self.server_port))
            sock.settimeout(10.0)
            
            logging.info(f"开始发送 {num_messages} 条消息，间隔 {delay}s")
            start_time = time.time()
            
            for i in range(num_messages):
                if not self.running:
                    break
                    
                try:
                    device_id = random.randint(1, 999999)
                    card_id = random.randint(1, 999999)
                    message = self.generate_test_message(device_id, card_id)
                    
                    sock.send(message.encode('utf-8'))
                    self.sent_count += 1
                    
                    if delay > 0:
                        time.sleep(delay)
                        
                    if (i + 1) % 100 == 0:
                        logging.info(f"已发送 {i + 1} 条消息")
                        
                except Exception as e:
                    self.error_count += 1
                    logging.error(f"发送消息失败: {str(e)}")
            
            end_time = time.time()
            duration = end_time - start_time
            
            logging.info(f"发送完成 - 总数: {self.sent_count}, 错误: {self.error_count}, 耗时: {duration:.2f}s, 速率: {self.sent_count/duration:.2f} msg/s")
            
        except Exception as e:
            logging.error(f"连接失败: {str(e)}")
        finally:
            try:
                sock.close()
            except:
                pass
    
    def stop(self):
        """停止发送"""
        self.running = False

def run_concurrent_test(num_clients=5, messages_per_client=200, delay=0.01):
    """运行并发测试"""
    logging.info(f"启动并发测试 - 客户端数: {num_clients}, 每客户端消息数: {messages_per_client}")
    
    clients = []
    threads = []
    
    # 创建客户端
    for i in range(num_clients):
        client = TCPTestClient()
        clients.append(client)
        
        thread = threading.Thread(
            target=client.send_messages,
            args=(messages_per_client, delay),
            name=f"Client-{i+1}"
        )
        threads.append(thread)
    
    # 启动所有线程
    start_time = time.time()
    for thread in threads:
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    end_time = time.time()
    duration = end_time - start_time
    
    # 统计结果
    total_sent = sum(client.sent_count for client in clients)
    total_errors = sum(client.error_count for client in clients)
    
    logging.info("=" * 60)
    logging.info("并发测试结果:")
    logging.info(f"客户端数量: {num_clients}")
    logging.info(f"总发送消息: {total_sent}")
    logging.info(f"总错误数: {total_errors}")
    logging.info(f"错误率: {(total_errors/total_sent*100):.2f}%" if total_sent > 0 else "N/A")
    logging.info(f"总耗时: {duration:.2f}s")
    logging.info(f"平均速率: {total_sent/duration:.2f} msg/s")
    logging.info("=" * 60)

def run_stress_test(duration_seconds=60, messages_per_second=100):
    """运行压力测试"""
    logging.info(f"启动压力测试 - 持续时间: {duration_seconds}s, 目标速率: {messages_per_second} msg/s")
    
    client = TCPTestClient()
    delay = 1.0 / messages_per_second if messages_per_second > 0 else 0
    
    # 计算需要发送的消息数
    total_messages = duration_seconds * messages_per_second
    
    thread = threading.Thread(
        target=client.send_messages,
        args=(total_messages, delay),
        name="StressTest"
    )
    
    thread.start()
    
    # 监控进度
    start_time = time.time()
    while thread.is_alive():
        time.sleep(5)
        elapsed = time.time() - start_time
        current_rate = client.sent_count / elapsed if elapsed > 0 else 0
        logging.info(f"压力测试进度 - 已发送: {client.sent_count}, 错误: {client.error_count}, 当前速率: {current_rate:.2f} msg/s")
    
    thread.join()

if __name__ == "__main__":
    print("TCP服务器性能测试工具")
    print("1. 并发测试 (5个客户端，每个发送200条消息)")
    print("2. 压力测试 (60秒，100 msg/s)")
    print("3. 自定义测试")
    
    choice = input("请选择测试类型 (1-3): ").strip()
    
    if choice == "1":
        run_concurrent_test(num_clients=5, messages_per_client=200, delay=0.01)
    elif choice == "2":
        run_stress_test(duration_seconds=60, messages_per_second=100)
    elif choice == "3":
        try:
            num_clients = int(input("客户端数量: "))
            messages_per_client = int(input("每客户端消息数: "))
            delay = float(input("消息间隔(秒): "))
            run_concurrent_test(num_clients, messages_per_client, delay)
        except ValueError:
            print("输入格式错误")
    else:
        print("无效选择")
