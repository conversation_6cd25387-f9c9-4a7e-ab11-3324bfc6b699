# API客户端模块：负责与远程API服务器通信，上传芯片扫描数据
# 包含重试机制、请求日志记录、异常处理等功能
import json
import logging
import requests
import time
from requests.exceptions import HTTPError, RequestException
from config import API_URL, MAX_RETRY_ATTEMPTS, REQUEST_TIMEOUT

# 创建模块级别的logger
logger = logging.getLogger('api_client')

class APIClient:
    @staticmethod
    def _post_to_api(data, logger, desc=""):
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "DeviceDataClient/1.0"
        }
        for attempt in range(MAX_RETRY_ATTEMPTS):
            try:
                if attempt > 0:
                    logger.warning(f"{desc} 正在进行第 {attempt + 1}/{MAX_RETRY_ATTEMPTS} 次重试")
                response = requests.post(API_URL, json=data, headers=headers, timeout=REQUEST_TIMEOUT)
                logger.debug(f"收到响应\n状态码: {response.status_code}\n响应头: {dict(response.headers)}\n响应内容: {response.text}")
                response.raise_for_status()
                logger.info(f"{desc} 数据发送成功 | 状态码: {response.status_code}")
                return True
            except HTTPError as e:
                error_msg = f"HTTP 错误 | 状态码: {response.status_code} | 响应内容: {response.text.strip()}"
                logger.error(error_msg)
                if attempt == MAX_RETRY_ATTEMPTS - 1:
                    logger.error(f"失败请求内容: {json.dumps(data, ensure_ascii=False)}")
            except RequestException as e:
                error_msg = f"请求异常 [{type(e).__name__}] | 错误信息: {str(e)}"
                logger.error(error_msg)
            except Exception as e:
                logger.exception(f"未处理的异常: {str(e)}")
                break
            sleep_time = min(2 ** attempt, 30)
            logger.debug(f"等待 {sleep_time} 秒后重试")
            time.sleep(sleep_time)
        logger.error(f"{desc} 数据发送失败 | 最大重试次数已达 ({MAX_RETRY_ATTEMPTS} 次)")
        return False

    @staticmethod
    def send_batch_data(records):
        if not records:
            return True
        # 使用模块级别的logger
        # 按设备ID分组处理
        device_groups = {}
        for record in records:
            if record.device_id not in device_groups:
                device_groups[record.device_id] = []
            device_groups[record.device_id].append(record)
        all_success = True
        for device_id, device_records in device_groups.items():
            tag_list = [{
                "epc": record.chip_id,
                "firstTime": record.scan_time
            } for record in device_records]
            data = {
                "data": {
                    "tagList": tag_list,
                    "devMark": device_id
                }
            }
            logger.debug(f"准备批量发送请求 | 设备: {device_id} | 记录数: {len(device_records)}")
            success = APIClient._post_to_api(data, logger, desc=f"批量-设备{device_id}")
            if not success:
                logger.error(f"批量数据发送失败 | 设备: {device_id} | 记录数: {len(device_records)}")
                all_success = False
        return all_success

    @staticmethod
    def send_data(record):
        # 使用模块级别的logger
        data = {
            "data": {
                "tagList": [{
                    "epc": record.chip_id,
                    "firstTime": record.scan_time
                }],
                "devMark": record.device_id
            }
        }
        logger.debug("准备发送请求\nURL: %s\nHeaders: %s\nBody: %s", API_URL, {
            "Content-Type": "application/json",
            "User-Agent": "DeviceDataClient/1.0"
        }, json.dumps(data, indent=2, ensure_ascii=False))
        return APIClient._post_to_api(data, logger, desc="单条")