#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
消息格式调试工具
用于分析和调试TCP消息格式问题
"""

import socket
import threading
import time
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class MessageDebugger:
    def __init__(self, server_host='localhost', server_port=2000):
        self.server_host = server_host
        self.server_port = server_port
        self.MESSAGE_LENGTH = 40
        
    def analyze_raw_data(self, data):
        """分析原始数据"""
        print(f"\n{'='*60}")
        print(f"原始数据分析:")
        print(f"数据长度: {len(data)} 字节")
        print(f"原始字节: {data}")
        
        try:
            decoded = data.decode('utf-8', errors='replace')
            print(f"UTF-8解码: '{decoded}'")
            print(f"解码长度: {len(decoded)} 字符")
        except Exception as e:
            print(f"UTF-8解码失败: {e}")
        
        # 查找 # 字符
        hash_positions = []
        for i, byte in enumerate(data):
            if byte == ord('#'):
                hash_positions.append(i)
        
        print(f"找到 # 字符位置: {hash_positions}")
        
        # 分析可能的消息
        for pos in hash_positions:
            if pos + self.MESSAGE_LENGTH <= len(data):
                potential_msg = data[pos:pos + self.MESSAGE_LENGTH]
                try:
                    decoded_msg = potential_msg.decode('utf-8', errors='replace')
                    print(f"位置 {pos} 的潜在消息: '{decoded_msg}'")
                    self.validate_message_format(decoded_msg)
                except Exception as e:
                    print(f"位置 {pos} 解码失败: {e}")
        
        print(f"{'='*60}\n")
    
    def validate_message_format(self, message):
        """验证消息格式"""
        print(f"  消息格式验证:")
        print(f"    长度: {len(message)} (期望: {self.MESSAGE_LENGTH})")
        print(f"    开头: '{message[0] if message else 'N/A'}' (期望: '#')")
        
        if len(message) == self.MESSAGE_LENGTH and message.startswith('#'):
            print(f"    ✓ 格式正确")
            # 解析具体字段
            try:
                device_number = message[1:9]
                card_number = message[25:31]
                print(f"    设备编号: '{device_number}'")
                print(f"    卡片编号: '{card_number}'")
            except Exception as e:
                print(f"    字段解析失败: {e}")
        else:
            print(f"    ✗ 格式错误")
    
    def monitor_tcp_stream(self, duration=30):
        """监控TCP数据流"""
        print(f"开始监控TCP数据流 {duration} 秒...")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.connect((self.server_host, self.server_port))
            sock.settimeout(1.0)
            
            buffer = b''
            start_time = time.time()
            message_count = 0
            
            while time.time() - start_time < duration:
                try:
                    data = sock.recv(1024)
                    if not data:
                        print("连接已关闭")
                        break
                    
                    print(f"\n接收到数据: {len(data)} 字节")
                    buffer += data
                    
                    # 分析缓冲区
                    self.analyze_buffer(buffer)
                    
                    # 尝试提取消息
                    extracted = self.extract_messages(buffer)
                    if extracted:
                        buffer, messages = extracted
                        for msg in messages:
                            message_count += 1
                            print(f"提取消息 #{message_count}: '{msg}'")
                            self.validate_message_format(msg)
                    
                except socket.timeout:
                    continue
                except Exception as e:
                    print(f"接收数据异常: {e}")
                    break
            
            print(f"\n监控完成，共提取 {message_count} 条消息")
            
        except Exception as e:
            print(f"连接失败: {e}")
        finally:
            try:
                sock.close()
            except:
                pass
    
    def analyze_buffer(self, buffer):
        """分析缓冲区内容"""
        if len(buffer) > 1000:  # 只分析较小的缓冲区
            return
            
        print(f"缓冲区分析: {len(buffer)} 字节")
        
        # 查找所有 # 字符
        hash_positions = []
        for i in range(len(buffer)):
            if buffer[i:i+1] == b'#':
                hash_positions.append(i)
        
        if hash_positions:
            print(f"  # 字符位置: {hash_positions}")
        else:
            print(f"  未找到 # 字符")
            # 显示前20个字节的内容
            preview = buffer[:20]
            print(f"  缓冲区前20字节: {preview}")
            try:
                decoded_preview = preview.decode('utf-8', errors='replace')
                print(f"  解码预览: '{decoded_preview}'")
            except:
                pass
    
    def extract_messages(self, buffer):
        """从缓冲区提取消息"""
        try:
            messages = []
            remaining_buffer = buffer
            
            while len(remaining_buffer) >= self.MESSAGE_LENGTH:
                # 寻找 # 开头
                start_pos = remaining_buffer.find(b'#')
                if start_pos == -1:
                    break
                
                if start_pos + self.MESSAGE_LENGTH > len(remaining_buffer):
                    break
                
                # 提取消息
                message_bytes = remaining_buffer[start_pos:start_pos + self.MESSAGE_LENGTH]
                try:
                    message = message_bytes.decode('utf-8', errors='replace')
                    if len(message) == self.MESSAGE_LENGTH:
                        messages.append(message)
                        remaining_buffer = remaining_buffer[start_pos + self.MESSAGE_LENGTH:]
                    else:
                        remaining_buffer = remaining_buffer[start_pos + 1:]
                except:
                    remaining_buffer = remaining_buffer[start_pos + 1:]
            
            return remaining_buffer, messages
            
        except Exception as e:
            print(f"提取消息异常: {e}")
            return buffer, []
    
    def send_test_message(self):
        """发送测试消息"""
        test_message = "#FC0011112506192129040000000000A00000086"
        print(f"发送测试消息: '{test_message}' (长度: {len(test_message)})")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.connect((self.server_host, self.server_port))
            
            sock.send(test_message.encode('utf-8'))
            print("测试消息发送成功")
            
            time.sleep(1)
            sock.close()
            
        except Exception as e:
            print(f"发送测试消息失败: {e}")

def main():
    debugger = MessageDebugger()
    
    print("消息格式调试工具")
    print("1. 发送测试消息")
    print("2. 监控TCP数据流")
    print("3. 分析原始数据")
    
    choice = input("请选择操作 (1-3): ").strip()
    
    if choice == "1":
        debugger.send_test_message()
    elif choice == "2":
        duration = int(input("监控时长(秒): ") or "30")
        debugger.monitor_tcp_stream(duration)
    elif choice == "3":
        raw_input = input("输入原始数据(十六进制或字符串): ").strip()
        try:
            if raw_input.startswith('0x') or all(c in '0123456789abcdefABCDEF' for c in raw_input):
                # 十六进制输入
                data = bytes.fromhex(raw_input.replace('0x', ''))
            else:
                # 字符串输入
                data = raw_input.encode('utf-8')
            debugger.analyze_raw_data(data)
        except Exception as e:
            print(f"数据解析失败: {e}")
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
