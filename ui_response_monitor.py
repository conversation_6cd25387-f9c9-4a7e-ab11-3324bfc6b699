#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI响应性监控工具
用于监控界面更新延迟和响应性问题
"""

import time
import threading
import logging
from datetime import datetime
from collections import deque
import socket

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class UIResponseMonitor:
    def __init__(self, server_host='localhost', server_port=2000):
        self.server_host = server_host
        self.server_port = server_port
        self.monitoring = False
        self.send_times = deque(maxlen=100)
        self.response_delays = deque(maxlen=100)
        
    def start_monitoring(self, test_duration=30, send_interval=1.0):
        """开始监控UI响应性"""
        print(f"开始UI响应性测试 - 持续{test_duration}秒，发送间隔{send_interval}秒")
        
        self.monitoring = True
        
        # 启动发送线程
        send_thread = threading.Thread(
            target=self._send_test_data,
            args=(test_duration, send_interval),
            daemon=True
        )
        send_thread.start()
        
        # 主线程监控
        start_time = time.time()
        last_report_time = start_time
        
        while time.time() - start_time < test_duration and self.monitoring:
            current_time = time.time()
            
            # 每5秒报告一次
            if current_time - last_report_time >= 5:
                self._report_status()
                last_report_time = current_time
            
            time.sleep(1)
        
        self.monitoring = False
        send_thread.join(timeout=2)
        
        # 最终报告
        self._final_report()
    
    def _send_test_data(self, duration, interval):
        """发送测试数据"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.connect((self.server_host, self.server_port))
            
            start_time = time.time()
            message_count = 0
            
            while time.time() - start_time < duration and self.monitoring:
                try:
                    # 生成测试消息
                    device_id = f"TEST{message_count:04d}"
                    card_id = f"{message_count:06d}"
                    message = self._generate_test_message(device_id, card_id)
                    
                    # 记录发送时间
                    send_time = time.time()
                    self.send_times.append(send_time)
                    
                    # 发送消息
                    sock.send(message.encode('utf-8'))
                    message_count += 1
                    
                    print(f"发送测试消息 #{message_count}: {device_id} - {card_id}")
                    
                    time.sleep(interval)
                    
                except Exception as e:
                    logging.error(f"发送测试数据失败: {str(e)}")
                    break
            
            print(f"测试数据发送完成，共发送 {message_count} 条消息")
            
        except Exception as e:
            logging.error(f"连接测试服务器失败: {str(e)}")
        finally:
            try:
                sock.close()
            except:
                pass
    
    def _generate_test_message(self, device_id, card_id):
        """生成测试消息"""
        # 消息格式: #FC0011112506192129040000000000A00000086
        device_str = device_id[:8].ljust(8, '0')
        card_str = card_id[:6].ljust(6, '0')
        
        # 构造40字节消息
        message = f"#{device_str}2506192129040000000000{card_str}00000086"
        
        # 确保消息长度为40
        if len(message) < 40:
            message += '0' * (40 - len(message))
        elif len(message) > 40:
            message = message[:40]
            
        return message
    
    def _report_status(self):
        """报告当前状态"""
        if not self.send_times:
            return
            
        current_time = time.time()
        recent_sends = [t for t in self.send_times if current_time - t <= 10]  # 最近10秒
        
        print(f"\n📊 实时状态报告:")
        print(f"   最近10秒发送: {len(recent_sends)} 条消息")
        print(f"   总发送数: {len(self.send_times)} 条消息")
        
        if len(self.send_times) >= 2:
            avg_interval = (self.send_times[-1] - self.send_times[0]) / (len(self.send_times) - 1)
            print(f"   平均发送间隔: {avg_interval:.2f} 秒")
    
    def _final_report(self):
        """最终报告"""
        print("\n" + "="*60)
        print("📋 UI响应性测试报告")
        print("="*60)
        
        if not self.send_times:
            print("❌ 没有发送任何测试数据")
            return
        
        total_messages = len(self.send_times)
        test_duration = self.send_times[-1] - self.send_times[0] if len(self.send_times) > 1 else 0
        
        print(f"测试统计:")
        print(f"  总发送消息: {total_messages}")
        print(f"  测试时长: {test_duration:.1f} 秒")
        print(f"  平均发送速率: {total_messages/test_duration:.2f} msg/s" if test_duration > 0 else "  平均发送速率: N/A")
        
        # 分析发送间隔
        if len(self.send_times) >= 2:
            intervals = []
            for i in range(1, len(self.send_times)):
                interval = self.send_times[i] - self.send_times[i-1]
                intervals.append(interval)
            
            if intervals:
                import numpy as np
                print(f"\n发送间隔分析:")
                print(f"  平均间隔: {np.mean(intervals):.3f} 秒")
                print(f"  间隔标准差: {np.std(intervals):.3f} 秒")
                print(f"  最大间隔: {max(intervals):.3f} 秒")
                print(f"  最小间隔: {min(intervals):.3f} 秒")
        
        print("\n💡 UI响应性建议:")
        print("  1. 观察界面是否在1-2秒内显示新数据")
        print("  2. 检查数据是否连续滚动，无明显停顿")
        print("  3. 如果有3-4秒延迟，说明UI更新机制需要优化")
        print("  4. 理想情况下，数据应该在500ms内显示")
        
        print("="*60)
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False

class SimpleUITest:
    """简单的UI测试工具"""
    
    @staticmethod
    def quick_test():
        """快速测试"""
        print("🚀 快速UI响应性测试")
        print("这个测试会发送10条消息，观察界面响应")
        
        monitor = UIResponseMonitor()
        
        try:
            # 发送10条测试消息
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.connect(('localhost', 2000))
            
            for i in range(10):
                message = f"#TEST{i:04d}2506192129040000000000{i:06d}00000086"
                sock.send(message.encode('utf-8'))
                print(f"✅ 发送测试消息 #{i+1}")
                time.sleep(0.5)  # 每0.5秒发送一条
            
            print("\n✅ 测试完成！请观察界面是否在1-2秒内显示这10条测试数据")
            print("如果数据显示有明显延迟，说明UI更新需要优化")
            
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
        finally:
            try:
                sock.close()
            except:
                pass

def main():
    print("UI响应性监控工具")
    print("1. 快速测试 (10条消息)")
    print("2. 完整测试 (30秒)")
    print("3. 自定义测试")
    print("4. 退出")
    
    choice = input("请选择测试类型 (1-4): ").strip()
    
    if choice == "1":
        SimpleUITest.quick_test()
    elif choice == "2":
        monitor = UIResponseMonitor()
        monitor.start_monitoring(30, 1.0)
    elif choice == "3":
        try:
            duration = int(input("测试时长(秒): "))
            interval = float(input("发送间隔(秒): "))
            monitor = UIResponseMonitor()
            monitor.start_monitoring(duration, interval)
        except ValueError:
            print("输入格式错误")
    elif choice == "4":
        print("退出")
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
