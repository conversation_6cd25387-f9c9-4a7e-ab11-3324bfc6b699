#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
连接测试工具
用于测试设备列表显示功能
"""

import socket
import time
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_connection():
    """测试连接功能"""
    print("🔌 测试设备连接功能")
    print("这个工具会连接到服务器，然后断开，观察设备列表是否正常显示")
    print("-" * 50)
    
    try:
        # 连接到服务器
        print("正在连接到服务器...")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.connect(('localhost', 2000))
        print("✅ 连接成功！")
        print("请检查界面上的设备列表是否显示了新连接")
        
        # 保持连接一段时间
        print("保持连接10秒...")
        for i in range(10):
            print(f"连接中... {10-i}秒")
            time.sleep(1)
        
        # 发送一条测试消息
        test_message = "#TEST0001250619212904000000000000100000086"
        sock.send(test_message.encode('utf-8'))
        print("✅ 发送了一条测试消息")
        
        # 再保持连接5秒
        print("再保持连接5秒...")
        for i in range(5):
            print(f"连接中... {5-i}秒")
            time.sleep(1)
        
        print("正在断开连接...")
        sock.close()
        print("✅ 连接已断开！")
        print("请检查界面上的设备列表是否移除了断开的连接")
        
    except ConnectionRefusedError:
        print("❌ 连接被拒绝 - 请确保服务器已启动")
    except Exception as e:
        print(f"❌ 连接测试失败: {str(e)}")

def test_multiple_connections():
    """测试多个连接"""
    print("🔌 测试多个设备连接")
    print("这个工具会创建3个连接，观察设备列表显示")
    print("-" * 50)
    
    sockets = []
    
    try:
        # 创建3个连接
        for i in range(3):
            print(f"创建连接 #{i+1}...")
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.connect(('localhost', 2000))
            sockets.append(sock)
            print(f"✅ 连接 #{i+1} 成功")
            time.sleep(2)  # 间隔2秒
        
        print(f"✅ 已创建 {len(sockets)} 个连接")
        print("请检查界面上是否显示了3个设备连接")
        
        # 保持连接10秒
        print("保持所有连接10秒...")
        time.sleep(10)
        
        # 逐个断开连接
        for i, sock in enumerate(sockets):
            print(f"断开连接 #{i+1}...")
            sock.close()
            print(f"✅ 连接 #{i+1} 已断开")
            time.sleep(2)  # 间隔2秒观察
        
        print("✅ 所有连接已断开")
        print("请检查界面上的设备列表是否已清空")
        
    except Exception as e:
        print(f"❌ 多连接测试失败: {str(e)}")
        # 清理连接
        for sock in sockets:
            try:
                sock.close()
            except:
                pass

def main():
    print("设备连接测试工具")
    print("1. 单个连接测试")
    print("2. 多个连接测试")
    print("3. 退出")
    
    choice = input("请选择测试类型 (1-3): ").strip()
    
    if choice == "1":
        test_connection()
    elif choice == "2":
        test_multiple_connections()
    elif choice == "3":
        print("退出")
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
