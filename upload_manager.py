#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立的上传管理器
负责处理所有API上传相关的操作，与数据读取完全分离
"""

import threading
import logging
from datetime import datetime
from collections import deque
from concurrent.futures import ThreadPoolExecutor
from PyQt5.QtCore import QObject, QTimer, QMutex, QMutexLocker, pyqtSignal
from api_client import APIClient
from config import (
    MAX_RETRY_ATTEMPTS, API_THREADS, API_BATCH_SIZE, 
    API_BATCH_INTERVAL, API_MAX_QUEUE_SIZE
)

class UploadManager(QObject):
    """独立的上传管理器，与界面和数据读取完全分离"""

    # 添加状态更新信号
    status_update_signal = pyqtSignal(object)  # 发送状态更新的记录

    def __init__(self):
        super().__init__()
        self.running = False
        
        # API相关队列和线程池
        self.upload_queue = deque()  # 待上传队列
        self.retry_queue = deque()   # 重试队列
        self.upload_mutex = QMutex()
        self.retry_mutex = QMutex()
        
        # API客户端和线程池
        self.api_client = APIClient()
        self.api_executor = ThreadPoolExecutor(max_workers=API_THREADS, thread_name_prefix="Upload")
        
        # 批量上传定时器
        self.batch_timer = QTimer()
        self.batch_timer.setInterval(API_BATCH_INTERVAL)
        self.batch_timer.timeout.connect(self._process_upload_batch)
        
        # 重试定时器
        self.retry_timer = QTimer()
        self.retry_timer.setInterval(20000)  # 20秒重试一次
        self.retry_timer.timeout.connect(self._process_retry_queue)
        
        # 统计信息
        self.stats = {
            'total_uploaded': 0,
            'total_failed': 0,
            'queue_size': 0,
            'retry_size': 0,
            'last_upload_time': None
        }
        self.stats_mutex = QMutex()
        
        logging.info("上传管理器初始化完成")
    
    def start(self):
        """启动上传管理器"""
        self.running = True
        self.batch_timer.start()
        self.retry_timer.start()
        logging.info("上传管理器已启动")
    
    def stop(self):
        """停止上传管理器"""
        logging.info("开始停止上传管理器...")

        # 首先设置停止标志
        self.running = False

        # 立即停止定时器，防止新任务提交
        self.batch_timer.stop()
        self.retry_timer.stop()
        logging.info("定时器已停止")

        # 等待一小段时间，让正在执行的定时器方法完成
        import time
        time.sleep(0.1)

        # 等待所有上传任务完成
        try:
            logging.info("等待上传任务完成...")
            self.api_executor.shutdown(wait=True)
            logging.info("上传管理器已停止")
        except Exception as e:
            logging.error(f"停止上传管理器异常: {str(e)}")
            # 强制关闭
            try:
                self.api_executor.shutdown(wait=False)
            except:
                pass
    
    def add_record(self, record):
        """添加记录到上传队列（非阻塞）"""
        if not self.running:
            return
            
        with QMutexLocker(self.upload_mutex):
            # 检查队列大小，防止内存溢出
            if len(self.upload_queue) >= API_MAX_QUEUE_SIZE:
                # 丢弃最旧的记录
                dropped_count = len(self.upload_queue) - API_MAX_QUEUE_SIZE + 1000
                for _ in range(dropped_count):
                    if self.upload_queue:
                        dropped = self.upload_queue.popleft()
                        logging.warning(f"上传队列满，丢弃记录: {dropped.chip_id}")
            
            # 创建记录副本，避免状态污染
            upload_record = self._create_upload_copy(record)
            self.upload_queue.append(upload_record)
    
    def _create_upload_copy(self, record):
        """创建用于上传的记录副本"""
        from data_record import DataRecord
        upload_record = DataRecord(
            record.chip_id.replace('202501', ''),  # 移除前缀，恢复原始ID
            record.device_id,
            record.scan_time
        )
        upload_record.status = "待上传"
        return upload_record
    
    def _process_upload_batch(self):
        """处理批量上传"""
        if not self.running:
            return

        batch = []
        with QMutexLocker(self.upload_mutex):
            if not self.upload_queue:
                return

            # 获取批量数据
            batch_size = min(len(self.upload_queue), API_BATCH_SIZE)
            for _ in range(batch_size):
                if self.upload_queue:
                    batch.append(self.upload_queue.popleft())

        if batch and self.running:
            # 检查线程池是否还在运行，避免shutdown后提交任务
            try:
                self.api_executor.submit(self._upload_batch, batch)
            except RuntimeError as e:
                if "shutdown" in str(e):
                    logging.warning("上传管理器已关闭，跳过批量上传")
                    return
                else:
                    raise
    
    def _upload_batch(self, batch):
        """执行批量上传"""
        try:
            logging.info(f"开始批量上传 {len(batch)} 条记录")
            success = self.api_client.send_batch_data(batch)
            
            # 更新每个记录的状态并发送信号
            for record in batch:
                if success:
                    record.status = "已成功"
                    # 发送状态更新信号到界面
                    self.status_update_signal.emit(record)
                else:
                    record.status = "已失败"
                    record.last_retry = datetime.now()
                    # 发送状态更新信号到界面
                    self.status_update_signal.emit(record)

            # 更新统计信息
            with QMutexLocker(self.stats_mutex):
                if success:
                    self.stats['total_uploaded'] += len(batch)
                    self.stats['last_upload_time'] = datetime.now()
                    logging.info(f"批量上传成功: {len(batch)} 条记录")
                else:
                    self.stats['total_failed'] += len(batch)
                    # 将失败的记录加入重试队列
                    self._add_to_retry_queue(batch)
                    logging.warning(f"批量上传失败: {len(batch)} 条记录，已加入重试队列")
                    
        except Exception as e:
            logging.error(f"批量上传异常: {str(e)}")

            # 更新每个记录的状态为失败
            for record in batch:
                record.status = "已失败"
                record.last_retry = datetime.now()
                # 发送状态更新信号到界面
                self.status_update_signal.emit(record)

            with QMutexLocker(self.stats_mutex):
                self.stats['total_failed'] += len(batch)
            self._add_to_retry_queue(batch)
    
    def _add_to_retry_queue(self, records):
        """将记录添加到重试队列"""
        with QMutexLocker(self.retry_mutex):
            for record in records:
                if record.retry_count < MAX_RETRY_ATTEMPTS:
                    record.retry_count += 1
                    record.last_retry = datetime.now()
                    self.retry_queue.append(record)
    
    def _process_retry_queue(self):
        """处理重试队列"""
        if not self.running:
            return
            
        retry_records = []
        with QMutexLocker(self.retry_mutex):
            current_time = datetime.now()
            
            # 检查需要重试的记录
            while self.retry_queue:
                record = self.retry_queue.popleft()
                
                # 检查是否到了重试时间
                if (current_time - record.last_retry).total_seconds() >= 20:
                    if record.retry_count < MAX_RETRY_ATTEMPTS:
                        retry_records.append(record)
                    else:
                        logging.warning(f"记录重试次数已达上限，放弃: {record.chip_id}")
                else:
                    # 还没到重试时间，放回队列
                    self.retry_queue.append(record)
                    break
        
        # 将重试记录重新加入上传队列
        if retry_records and self.running:
            with QMutexLocker(self.upload_mutex):
                self.upload_queue.extend(retry_records)
            logging.info(f"重新加入上传队列: {len(retry_records)} 条记录")
    
    def get_stats(self):
        """获取统计信息"""
        with QMutexLocker(self.stats_mutex):
            with QMutexLocker(self.upload_mutex):
                self.stats['queue_size'] = len(self.upload_queue)
            with QMutexLocker(self.retry_mutex):
                self.stats['retry_size'] = len(self.retry_queue)
            return self.stats.copy()
    
    def clear_queues(self):
        """清空所有队列"""
        with QMutexLocker(self.upload_mutex):
            cleared_upload = len(self.upload_queue)
            self.upload_queue.clear()
            
        with QMutexLocker(self.retry_mutex):
            cleared_retry = len(self.retry_queue)
            self.retry_queue.clear()
            
        logging.info(f"清空队列 - 上传队列: {cleared_upload}, 重试队列: {cleared_retry}")
        return cleared_upload + cleared_retry

# 全局上传管理器实例
_upload_manager = None

def get_upload_manager():
    """获取全局上传管理器实例"""
    global _upload_manager
    if _upload_manager is None:
        _upload_manager = UploadManager()
    return _upload_manager

def start_upload_manager():
    """启动全局上传管理器"""
    manager = get_upload_manager()
    manager.start()
    return manager

def stop_upload_manager():
    """停止全局上传管理器"""
    global _upload_manager
    if _upload_manager:
        _upload_manager.stop()
        _upload_manager = None
