#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立的上传管理器
负责处理所有API上传相关的操作，与数据读取完全分离
"""

import threading
import logging
from datetime import datetime
from collections import deque
from concurrent.futures import ThreadPoolExecutor
from PyQt5.QtCore import QObject, QTimer, QMutex, QMutexLocker, pyqtSignal
from api_client import APIClient
from config import (
    MAX_RETRY_ATTEMPTS, API_THREADS, API_BATCH_SIZE,
    API_BATCH_INTERVAL, API_MAX_QUEUE_SIZE
)

# 创建模块级别的logger
logger = logging.getLogger('upload_manager')

class UploadManager(QObject):
    """独立的上传管理器，与界面和数据读取完全分离"""

    # 添加状态更新信号（批量发送）
    status_update_signal = pyqtSignal(list)  # 发送状态更新的记录列表

    def __init__(self):
        super().__init__()
        self.running = False
        
        # API相关队列和线程池
        self.upload_queue = deque()  # 待上传队列
        self.retry_queue = deque()   # 重试队列
        self.upload_mutex = QMutex()
        self.retry_mutex = QMutex()
        
        # API客户端和线程池
        self.api_client = APIClient()
        self.api_executor = ThreadPoolExecutor(max_workers=API_THREADS, thread_name_prefix="Upload")
        
        # 批量上传定时器
        self.batch_timer = QTimer()
        self.batch_timer.setInterval(API_BATCH_INTERVAL)
        self.batch_timer.timeout.connect(self._process_upload_batch)
        
        # 重试定时器
        self.retry_timer = QTimer()
        self.retry_timer.setInterval(20000)  # 20秒重试一次
        self.retry_timer.timeout.connect(self._process_retry_queue)
        
        # 统计信息
        self.stats = {
            'total_uploaded': 0,
            'total_failed': 0,
            'queue_size': 0,
            'retry_size': 0,
            'last_upload_time': None
        }
        self.stats_mutex = QMutex()

        # 状态更新缓冲区，减少UI信号频率
        self.status_update_buffer = []
        self.status_buffer_mutex = QMutex()

        # 状态更新定时器（降低UI更新频率）
        self.status_update_timer = QTimer()
        self.status_update_timer.setInterval(1000)  # 1秒批量发送一次状态更新
        self.status_update_timer.timeout.connect(self._flush_status_updates)

        # 队列监控定时器
        self.queue_monitor_timer = QTimer()
        self.queue_monitor_timer.setInterval(10000)  # 10秒监控一次队列状态
        self.queue_monitor_timer.timeout.connect(self._monitor_queue_health)
        
        logger.info("上传管理器初始化完成")
    
    def start(self):
        """启动上传管理器"""
        self.running = True
        self.batch_timer.start()
        self.retry_timer.start()
        self.status_update_timer.start()  # 启动状态更新定时器
        self.queue_monitor_timer.start()  # 启动队列监控定时器
        logger.info("上传管理器已启动")

        # 测试日志输出
        logger.info("🚀 上传管理器日志测试 - 如果你看到这条消息，说明日志配置正确")
    
    def stop(self):
        """停止上传管理器"""
        logger.info("开始停止上传管理器...")

        # 立即停止定时器，防止新任务提交
        self.batch_timer.stop()
        self.retry_timer.stop()
        self.status_update_timer.stop()
        self.queue_monitor_timer.stop()
        logging.info("定时器已停止")

        # 处理所有剩余数据（在设置running=False之前）
        self._process_all_remaining_data()

        # 发送剩余的状态更新
        self._flush_status_updates(force=True)
        logging.info("已处理所有剩余数据")

        # 设置停止标志
        self.running = False

        # 等待一小段时间，让正在执行的任务完成
        import time
        time.sleep(0.5)  # 增加等待时间

        # 等待所有上传任务完成
        try:
            logging.info("等待上传任务完成...")
            self.api_executor.shutdown(wait=True)
            logging.info("上传管理器已停止")
        except Exception as e:
            logging.error(f"停止上传管理器异常: {str(e)}")
            # 强制关闭
            try:
                self.api_executor.shutdown(wait=False)
            except:
                pass
    
    def add_record(self, record):
        """添加记录到上传队列（智能处理大数据量）"""
        if not self.running:
            return

        # 创建记录副本，避免状态污染
        upload_record = self._create_upload_copy(record)

        # 智能队列管理 - 不丢弃数据
        added = False
        retry_count = 0
        max_retries = 10

        while not added and retry_count < max_retries:
            with QMutexLocker(self.upload_mutex):
                current_size = len(self.upload_queue)

                if current_size < API_MAX_QUEUE_SIZE:
                    # 队列未满，直接添加
                    self.upload_queue.append(upload_record)
                    added = True

                elif current_size < API_MAX_QUEUE_SIZE * 1.2:  # 允许20%的缓冲
                    # 队列接近满，但仍可添加
                    self.upload_queue.append(upload_record)
                    added = True
                    if retry_count == 0:  # 只记录一次警告
                        logger.warning(f"上传队列接近满载: {current_size}/{API_MAX_QUEUE_SIZE}")
                else:
                    # 队列满，触发紧急处理
                    logger.warning(f"上传队列满载，触发紧急处理: {current_size}")

            if not added:
                # 队列满时，立即触发一次批量处理
                self._process_upload_batch()

                # 短暂等待，让队列有机会被处理
                import time
                time.sleep(0.1)
                retry_count += 1

        if not added:
            # 如果重试多次仍无法添加，记录错误但不丢弃数据
            logger.error(f"无法添加记录到上传队列，队列持续满载: {record.chip_id}")
            # 可以考虑写入临时文件或其他备用存储
            self._handle_overflow_record(upload_record)
    
    def _create_upload_copy(self, record):
        """创建用于上传的记录副本"""
        from data_record import DataRecord
        upload_record = DataRecord(
            record.chip_id.replace('202501', ''),  # 移除前缀，恢复原始ID
            record.device_id,
            record.scan_time
        )
        upload_record.status = "待上传"
        return upload_record
    
    def _process_upload_batch(self, force_all=False):
        """处理批量上传"""
        if not self.running and not force_all:
            return

        batch = []
        with QMutexLocker(self.upload_mutex):
            if not self.upload_queue:
                return

            # 获取批量数据
            if force_all:
                # 强制处理所有剩余数据
                batch_size = len(self.upload_queue)
                logging.info(f"强制处理所有剩余数据: {batch_size} 条")
            else:
                batch_size = min(len(self.upload_queue), API_BATCH_SIZE)

            for _ in range(batch_size):
                if self.upload_queue:
                    batch.append(self.upload_queue.popleft())

        if batch:
            # 检查线程池是否还在运行，避免shutdown后提交任务
            try:
                if self.running:
                    self.api_executor.submit(self._upload_batch, batch)
                else:
                    # 如果已停止但需要强制处理，直接同步处理
                    logging.info(f"同步处理剩余数据: {len(batch)} 条")
                    self._upload_batch(batch)
            except RuntimeError as e:
                if "shutdown" in str(e):
                    logging.warning("上传管理器已关闭，跳过批量上传")
                    return
                else:
                    raise
    
    def _upload_batch(self, batch):
        """执行批量上传"""
        try:
            logger.info(f"开始批量上传 {len(batch)} 条记录")

            # 记录批次中的数据信息
            if batch:
                first_record = batch[0]
                last_record = batch[-1]
                logging.debug(f"批次范围: {first_record.chip_id} 到 {last_record.chip_id}")

            success = self.api_client.send_batch_data(batch)
            
            # 更新每个记录的状态
            updated_records = []
            for record in batch:
                if success:
                    record.status = "已成功"
                else:
                    record.status = "已失败"
                    record.last_retry = datetime.now()
                updated_records.append(record)

            # 将状态更新加入缓冲区，而不是立即发送
            if updated_records:
                self._buffer_status_updates(updated_records)

            # 更新统计信息
            with QMutexLocker(self.stats_mutex):
                if success:
                    self.stats['total_uploaded'] += len(batch)
                    self.stats['last_upload_time'] = datetime.now()
                    logger.info(f"批量上传成功: {len(batch)} 条记录")
                else:
                    self.stats['total_failed'] += len(batch)
                    # 将失败的记录加入重试队列
                    self._add_to_retry_queue(batch)
                    logger.warning(f"批量上传失败: {len(batch)} 条记录，已加入重试队列")
                    
        except Exception as e:
            logging.error(f"批量上传异常: {str(e)}")

            # 更新每个记录的状态为失败
            failed_records = []
            for record in batch:
                record.status = "已失败"
                record.last_retry = datetime.now()
                failed_records.append(record)

            # 将状态更新加入缓冲区
            if failed_records:
                self._buffer_status_updates(failed_records)

            with QMutexLocker(self.stats_mutex):
                self.stats['total_failed'] += len(batch)
            self._add_to_retry_queue(batch)
    
    def _add_to_retry_queue(self, records):
        """将记录添加到重试队列"""
        with QMutexLocker(self.retry_mutex):
            for record in records:
                if record.retry_count < MAX_RETRY_ATTEMPTS:
                    record.retry_count += 1
                    record.last_retry = datetime.now()
                    self.retry_queue.append(record)
    
    def _process_retry_queue(self):
        """处理重试队列"""
        if not self.running:
            return
            
        retry_records = []
        with QMutexLocker(self.retry_mutex):
            current_time = datetime.now()
            
            # 检查需要重试的记录
            while self.retry_queue:
                record = self.retry_queue.popleft()
                
                # 检查是否到了重试时间
                if (current_time - record.last_retry).total_seconds() >= 20:
                    if record.retry_count < MAX_RETRY_ATTEMPTS:
                        retry_records.append(record)
                    else:
                        logging.warning(f"记录重试次数已达上限，放弃: {record.chip_id}")
                else:
                    # 还没到重试时间，放回队列
                    self.retry_queue.append(record)
                    break
        
        # 将重试记录重新加入上传队列
        if retry_records and self.running:
            with QMutexLocker(self.upload_mutex):
                self.upload_queue.extend(retry_records)
            logging.info(f"重新加入上传队列: {len(retry_records)} 条记录")
    
    def get_stats(self):
        """获取统计信息"""
        with QMutexLocker(self.stats_mutex):
            with QMutexLocker(self.upload_mutex):
                self.stats['queue_size'] = len(self.upload_queue)
            with QMutexLocker(self.retry_mutex):
                self.stats['retry_size'] = len(self.retry_queue)
            return self.stats.copy()
    
    def clear_queues(self):
        """清空所有队列"""
        with QMutexLocker(self.upload_mutex):
            cleared_upload = len(self.upload_queue)
            self.upload_queue.clear()
            
        with QMutexLocker(self.retry_mutex):
            cleared_retry = len(self.retry_queue)
            self.retry_queue.clear()
            
        logging.info(f"清空队列 - 上传队列: {cleared_upload}, 重试队列: {cleared_retry}")
        return cleared_upload + cleared_retry

    def flush_remaining_data(self):
        """手动刷新所有剩余数据（用于外部调用）"""
        logging.info("手动刷新剩余数据...")

        # 处理剩余的上传队列
        queue_size = 0
        with QMutexLocker(self.upload_mutex):
            queue_size = len(self.upload_queue)

        if queue_size > 0:
            logging.info(f"处理剩余上传队列: {queue_size} 条")
            self._process_upload_batch()

        # 处理剩余的状态更新
        buffer_size = 0
        with QMutexLocker(self.status_buffer_mutex):
            buffer_size = len(self.status_update_buffer)

        if buffer_size > 0:
            logging.info(f"处理剩余状态更新: {buffer_size} 条")
            self._flush_status_updates()

        if queue_size == 0 and buffer_size == 0:
            logging.info("没有剩余数据需要处理")

    def _process_all_remaining_data(self):
        """处理所有剩余的上传数据（停止时使用）"""
        total_processed = 0

        # 循环处理直到队列为空
        while True:
            batch = []
            with QMutexLocker(self.upload_mutex):
                if not self.upload_queue:
                    break

                # 每次处理一批数据
                batch_size = min(len(self.upload_queue), API_BATCH_SIZE)
                for _ in range(batch_size):
                    if self.upload_queue:
                        batch.append(self.upload_queue.popleft())

            if not batch:
                break

            # 提交到线程池处理
            try:
                logging.info(f"提交剩余数据批次: {len(batch)} 条")
                self.api_executor.submit(self._upload_batch, batch)
                total_processed += len(batch)
            except Exception as e:
                logging.error(f"提交剩余数据异常: {str(e)}")
                # 如果线程池有问题，尝试同步处理
                try:
                    logging.info(f"同步处理剩余数据: {len(batch)} 条")
                    self._upload_batch(batch)
                    total_processed += len(batch)
                except Exception as sync_e:
                    logging.error(f"同步处理也失败: {str(sync_e)}")
                    break

        logging.info(f"总共处理剩余数据: {total_processed} 条")

    def _handle_overflow_record(self, record):
        """处理队列溢出的记录"""
        try:
            # 方案1：写入临时文件
            import os
            import json
            from datetime import datetime

            overflow_dir = "overflow_data"
            if not os.path.exists(overflow_dir):
                os.makedirs(overflow_dir)

            # 按日期创建文件
            date_str = datetime.now().strftime("%Y%m%d")
            overflow_file = os.path.join(overflow_dir, f"overflow_{date_str}.jsonl")

            # 将记录写入文件
            record_data = {
                'chip_id': record.chip_id,
                'device_id': record.device_id,
                'scan_time': record.scan_time,
                'overflow_time': datetime.now().isoformat()
            }

            with open(overflow_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(record_data, ensure_ascii=False) + '\n')

            logger.info(f"溢出记录已保存到文件: {record.chip_id}")

        except Exception as e:
            logger.error(f"处理溢出记录失败: {str(e)}")

    def _monitor_queue_health(self):
        """监控队列健康状态并自动优化"""
        if not self.running:
            return

        try:
            with QMutexLocker(self.upload_mutex):
                queue_size = len(self.upload_queue)
                queue_usage = queue_size / API_MAX_QUEUE_SIZE

            with QMutexLocker(self.retry_mutex):
                retry_size = len(self.retry_queue)

            # 队列使用率监控
            if queue_usage > 0.8:  # 超过80%使用率
                logger.warning(f"上传队列使用率过高: {queue_usage:.1%} ({queue_size}/{API_MAX_QUEUE_SIZE})")

                # 自动优化：增加处理频率
                if self.batch_timer.interval() > 200:
                    self.batch_timer.setInterval(200)  # 减少到200ms
                    logger.info("自动优化：减少批量处理间隔到200ms")

                # 触发额外的批量处理
                self._process_upload_batch()

            elif queue_usage > 0.9:  # 超过90%使用率，紧急处理
                logger.error(f"上传队列接近满载: {queue_usage:.1%}")

                # 紧急处理：连续处理多批
                for _ in range(3):
                    self._process_upload_batch()

            elif queue_usage < 0.3:  # 使用率低于30%，恢复正常间隔
                if self.batch_timer.interval() < API_BATCH_INTERVAL:
                    self.batch_timer.setInterval(API_BATCH_INTERVAL)
                    logger.debug("队列压力正常，恢复正常处理间隔")

            # 重试队列监控
            if retry_size > 1000:
                logger.warning(f"重试队列积压严重: {retry_size} 条记录")

            # 记录队列状态
            logger.debug(f"队列状态 - 上传: {queue_size}, 重试: {retry_size}, 使用率: {queue_usage:.1%}")

        except Exception as e:
            logger.error(f"队列监控异常: {str(e)}")

    def _buffer_status_updates(self, records):
        """将状态更新加入缓冲区，减少UI信号频率"""
        with QMutexLocker(self.status_buffer_mutex):
            self.status_update_buffer.extend(records)

    def _flush_status_updates(self, force=False):
        """定时刷新状态更新缓冲区"""
        if not self.running and not force:
            return

        updates_to_send = []
        with QMutexLocker(self.status_buffer_mutex):
            if self.status_update_buffer:
                updates_to_send = self.status_update_buffer.copy()
                self.status_update_buffer.clear()

        # 发送批量状态更新
        if updates_to_send:
            self.status_update_signal.emit(updates_to_send)
            if force:
                logging.info(f"强制发送剩余状态更新: {len(updates_to_send)} 条记录")
            else:
                logging.debug(f"发送批量状态更新: {len(updates_to_send)} 条记录")

# 全局上传管理器实例
_upload_manager = None

def get_upload_manager():
    """获取全局上传管理器实例"""
    global _upload_manager
    if _upload_manager is None:
        _upload_manager = UploadManager()
    return _upload_manager

def start_upload_manager():
    """启动全局上传管理器"""
    manager = get_upload_manager()
    manager.start()
    return manager

def stop_upload_manager():
    """停止全局上传管理器"""
    global _upload_manager
    if _upload_manager:
        # 记录停止前的状态
        stats = _upload_manager.get_stats()
        logging.info(f"停止上传管理器 - 队列大小: {stats.get('queue_size', 0)}, 重试队列: {stats.get('retry_size', 0)}")

        _upload_manager.stop()
        _upload_manager = None
        logging.info("全局上传管理器已清理")
