#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立的上传管理器
负责处理所有API上传相关的操作，与数据读取完全分离
"""

import threading
import logging
from datetime import datetime
from collections import deque
from concurrent.futures import ThreadPoolExecutor
from PyQt5.QtCore import QObject, QTimer, QMutex, QMutexLocker, pyqtSignal
from api_client import APIClient
from config import (
    MAX_RETRY_ATTEMPTS, API_THREADS, API_BATCH_SIZE,
    API_BATCH_INTERVAL, API_MAX_QUEUE_SIZE
)

# 创建模块级别的logger
logger = logging.getLogger('upload_manager')

class UploadManager(QObject):
    """独立的上传管理器，与界面和数据读取完全分离"""

    # 添加状态更新信号（批量发送）
    status_update_signal = pyqtSignal(list)  # 发送状态更新的记录列表

    def __init__(self):
        super().__init__()
        self.running = False
        
        # API相关队列和线程池
        self.upload_queue = deque()  # 待上传队列
        self.retry_queue = deque()   # 重试队列
        self.upload_mutex = QMutex()
        self.retry_mutex = QMutex()
        
        # API客户端和线程池
        self.api_client = APIClient()
        self.api_executor = ThreadPoolExecutor(max_workers=API_THREADS, thread_name_prefix="Upload")
        
        # 批量上传定时器
        self.batch_timer = QTimer()
        self.batch_timer.setInterval(API_BATCH_INTERVAL)
        self.batch_timer.timeout.connect(self._process_upload_batch)
        
        # 重试定时器
        self.retry_timer = QTimer()
        self.retry_timer.setInterval(20000)  # 20秒重试一次
        self.retry_timer.timeout.connect(self._process_retry_queue)
        
        # 统计信息
        self.stats = {
            'total_uploaded': 0,
            'total_failed': 0,
            'queue_size': 0,
            'retry_size': 0,
            'last_upload_time': None
        }
        self.stats_mutex = QMutex()

        # 状态更新缓冲区，减少UI信号频率
        self.status_update_buffer = []
        self.status_buffer_mutex = QMutex()

        # 状态更新定时器（降低UI更新频率）
        self.status_update_timer = QTimer()
        self.status_update_timer.setInterval(1000)  # 1秒批量发送一次状态更新
        self.status_update_timer.timeout.connect(self._flush_status_updates)

        # 队列监控定时器
        self.queue_monitor_timer = QTimer()
        self.queue_monitor_timer.setInterval(10000)  # 10秒监控一次队列状态
        self.queue_monitor_timer.timeout.connect(self._monitor_queue_health)
        
        logger.info("上传管理器初始化完成")
    
    def start(self):
        """启动上传管理器"""
        self.running = True
        self.batch_timer.start()
        self.retry_timer.start()
        self.status_update_timer.start()  # 启动状态更新定时器
        self.queue_monitor_timer.start()  # 启动队列监控定时器
        logger.info("上传管理器已启动")

        # 测试日志输出
        logger.info("🚀 上传管理器日志测试 - 如果你看到这条消息，说明日志配置正确")
    
    def stop(self):
        """停止上传管理器"""
        logger.info("🛑 开始停止上传管理器...")

        # 记录停止前的详细状态
        with QMutexLocker(self.upload_mutex):
            upload_queue_size = len(self.upload_queue)
        with QMutexLocker(self.retry_mutex):
            retry_queue_size = len(self.retry_queue)
        with QMutexLocker(self.status_buffer_mutex):
            status_buffer_size = len(self.status_update_buffer)

        logger.info("=" * 50)
        logger.info("📊 停止前队列状态:")
        logger.info(f"   上传队列: {upload_queue_size} 条")
        logger.info(f"   重试队列: {retry_queue_size} 条")
        logger.info(f"   状态缓冲: {status_buffer_size} 条")
        logger.info(f"   总待处理: {upload_queue_size + retry_queue_size} 条")
        logger.info("=" * 50)

        # 立即停止定时器，防止新任务提交
        self.batch_timer.stop()
        self.retry_timer.stop()
        self.status_update_timer.stop()
        self.queue_monitor_timer.stop()
        logger.info("⏹️  定时器已停止")

        # 处理所有剩余数据（在设置running=False之前）
        if upload_queue_size > 0 or retry_queue_size > 0:
            logger.info(f"🔄 开始处理 {upload_queue_size + retry_queue_size} 条剩余数据...")
            self._process_all_remaining_data()
        else:
            logger.info("✅ 没有剩余数据需要处理")

        # 发送剩余的状态更新
        if status_buffer_size > 0:
            logger.info(f"📤 发送 {status_buffer_size} 条剩余状态更新...")
            self._flush_status_updates(force=True)
        else:
            logger.info("✅ 没有剩余状态更新需要发送")

        # 设置停止标志
        self.running = False
        logger.info("🚫 设置停止标志")

        # 确保所有数据都被处理完成
        import time
        logger.info("⏳ 确保所有数据处理完成...")

        # 循环处理直到队列真正为空
        max_wait_cycles = 30  # 最多等待30个周期
        cycle_count = 0

        while cycle_count < max_wait_cycles:
            # 检查队列状态
            with QMutexLocker(self.upload_mutex):
                upload_size = len(self.upload_queue)
            with QMutexLocker(self.retry_mutex):
                retry_size = len(self.retry_queue)

            if upload_size == 0 and retry_size == 0:
                logger.info("✅ 所有队列已清空")
                break

            logger.info(f"📊 第{cycle_count+1}轮检查: 上传队列{upload_size}条, 重试队列{retry_size}条")

            # 强制处理一轮
            if upload_size > 0:
                self._process_upload_batch(force_all=True)
            if retry_size > 0:
                self._process_retry_queue()

            # 等待处理完成
            time.sleep(1.0)
            cycle_count += 1

        if cycle_count >= max_wait_cycles:
            logger.warning("⚠️ 达到最大等待周期，强制继续")

        # 等待所有上传任务完成
        try:
            logger.info("⏳ 等待线程池关闭...")
            # 给线程池充足时间完成所有任务
            self.api_executor.shutdown(wait=True)
            logger.info("✅ 线程池已关闭")

            # 多次发送状态更新，确保不丢失
            for i in range(3):
                logger.info(f"📤 第{i+1}次发送状态更新...")
                self._flush_status_updates(force=True)
                time.sleep(0.5)

            logger.info("✅ 上传管理器已完全停止")

        except Exception as e:
            logger.error(f"❌ 停止上传管理器异常: {str(e)}")
            # 强制关闭
            try:
                self.api_executor.shutdown(wait=False)
            except:
                pass

        # 最终状态检查
        with QMutexLocker(self.upload_mutex):
            final_upload_size = len(self.upload_queue)
        with QMutexLocker(self.retry_mutex):
            final_retry_size = len(self.retry_queue)
        with QMutexLocker(self.status_buffer_mutex):
            final_status_size = len(self.status_update_buffer)

        logger.info("=" * 50)
        logger.info("📊 停止后队列状态:")
        logger.info(f"   上传队列: {final_upload_size} 条")
        logger.info(f"   重试队列: {final_retry_size} 条")
        logger.info(f"   状态缓冲: {final_status_size} 条")

        if final_upload_size > 0 or final_retry_size > 0:
            logger.warning(f"⚠️  仍有 {final_upload_size + final_retry_size} 条数据未处理!")
        else:
            logger.info("✅ 所有队列已清空")
        logger.info("=" * 50)
    
    def add_record(self, record):
        """添加记录到上传队列（智能处理大数据量）"""
        if not self.running:
            return

        # 创建记录副本，避免状态污染
        upload_record = self._create_upload_copy(record)

        # 智能队列管理 - 不丢弃数据
        added = False
        retry_count = 0
        max_retries = 10

        while not added and retry_count < max_retries:
            with QMutexLocker(self.upload_mutex):
                current_size = len(self.upload_queue)

                if current_size < API_MAX_QUEUE_SIZE:
                    # 队列未满，直接添加
                    self.upload_queue.append(upload_record)
                    added = True

                elif current_size < API_MAX_QUEUE_SIZE * 1.2:  # 允许20%的缓冲
                    # 队列接近满，但仍可添加
                    self.upload_queue.append(upload_record)
                    added = True
                    if retry_count == 0:  # 只记录一次警告
                        logger.warning(f"上传队列接近满载: {current_size}/{API_MAX_QUEUE_SIZE}")
                else:
                    # 队列满，触发紧急处理
                    logger.warning(f"上传队列满载，触发紧急处理: {current_size}")

            if not added:
                # 队列满时，立即触发一次批量处理
                self._process_upload_batch()

                # 短暂等待，让队列有机会被处理
                import time
                time.sleep(0.1)
                retry_count += 1

        if not added:
            # 如果重试多次仍无法添加，记录错误但不丢弃数据
            logger.error(f"无法添加记录到上传队列，队列持续满载: {record.chip_id}")
            # 可以考虑写入临时文件或其他备用存储
            self._handle_overflow_record(upload_record)
    
    def _create_upload_copy(self, record):
        """创建用于上传的记录副本"""
        from data_record import DataRecord
        upload_record = DataRecord(
            record.chip_id.replace('202501', ''),  # 移除前缀，恢复原始ID
            record.device_id,
            record.scan_time
        )
        upload_record.status = "待上传"
        return upload_record
    
    def _process_upload_batch(self, force_all=False):
        """处理批量上传"""
        try:
            if not self.running and not force_all:
                return

            batch = []
            with QMutexLocker(self.upload_mutex):
                if not self.upload_queue:
                    return

                # 获取批量数据
                if force_all:
                    # 强制处理所有剩余数据
                    batch_size = len(self.upload_queue)
                    logger.info(f"强制处理所有剩余数据: {batch_size} 条")
                else:
                    batch_size = min(len(self.upload_queue), API_BATCH_SIZE)

                for _ in range(batch_size):
                    if self.upload_queue:
                        batch.append(self.upload_queue.popleft())

            if batch:
                # 检查线程池是否还在运行，避免shutdown后提交任务
                try:
                    if self.running:
                        self.api_executor.submit(self._upload_batch, batch)
                    else:
                        # 如果已停止但需要强制处理，直接同步处理
                        logger.info(f"同步处理剩余数据: {len(batch)} 条")
                        self._upload_batch(batch)
                except RuntimeError as e:
                    if "shutdown" in str(e):
                        logger.warning("上传管理器已关闭，跳过批量上传")
                        return
                    else:
                        raise
        except KeyboardInterrupt:
            logger.info("收到中断信号，停止批量上传处理")
            raise
        except Exception as e:
            logger.error(f"批量上传处理异常: {str(e)}")
    
    def _upload_batch(self, batch):
        """执行批量上传 - 详细追踪版本"""
        batch_id = id(batch)
        try:
            logger.info(f"🚀 上传管理器开始处理批次 {batch_id}: {len(batch)} 条记录")

            # 记录批次中的数据信息
            if batch:
                first_record = batch[0]
                last_record = batch[-1]
                chip_ids = [record.chip_id for record in batch[:5]]  # 前5个芯片ID
                logger.info(f"📋 批次 {batch_id} 内容: {chip_ids}{'...' if len(batch) > 5 else ''}")
                logger.debug(f"📊 批次范围: {first_record.chip_id} 到 {last_record.chip_id}")

            # 记录上传前状态
            logger.info(f"📤 批次 {batch_id} 调用API客户端...")
            success = self.api_client.send_batch_data(batch)
            logger.info(f"📥 批次 {batch_id} API调用返回: {'成功' if success else '失败'}")
            
            # 详细更新每个记录的状态
            updated_records = []
            success_chips = []
            failed_chips = []

            for record in batch:
                old_status = getattr(record, 'status', '待上传')
                if success:
                    record.status = "已成功"
                    success_chips.append(record.chip_id)
                    logger.debug(f"✅ 记录状态更新: {record.chip_id} {old_status} → 已成功")
                else:
                    record.status = "已失败"
                    record.last_retry = datetime.now()
                    failed_chips.append(record.chip_id)
                    logger.debug(f"❌ 记录状态更新: {record.chip_id} {old_status} → 已失败")
                updated_records.append(record)

            # 记录状态更新汇总
            if success_chips:
                logger.info(f"✅ 批次 {batch_id} 成功记录: {len(success_chips)} 条")
            if failed_chips:
                logger.info(f"❌ 批次 {batch_id} 失败记录: {len(failed_chips)} 条")

            # 立即发送状态更新，确保不丢失
            if updated_records:
                logger.info(f"📤 批次 {batch_id} 发送状态更新到UI: {len(updated_records)} 条")
                self._buffer_status_updates(updated_records)
                # 立即发送，不等待定时器
                self._flush_status_updates(force=True)
                logger.info(f"✅ 批次 {batch_id} 状态更新已发送")

            # 更新统计信息
            with QMutexLocker(self.stats_mutex):
                if success:
                    self.stats['total_uploaded'] += len(batch)
                    self.stats['last_upload_time'] = datetime.now()
                    logger.info(f"批量上传成功: {len(batch)} 条记录")
                else:
                    self.stats['total_failed'] += len(batch)
                    # 将失败的记录加入重试队列
                    self._add_to_retry_queue(batch)
                    logger.warning(f"批量上传失败: {len(batch)} 条记录，已加入重试队列")
                    
        except Exception as e:
            logging.error(f"批量上传异常: {str(e)}")

            # 更新每个记录的状态为失败
            failed_records = []
            for record in batch:
                record.status = "已失败"
                record.last_retry = datetime.now()
                failed_records.append(record)

            # 立即发送状态更新
            if failed_records:
                self._buffer_status_updates(failed_records)
                self._flush_status_updates(force=True)

            with QMutexLocker(self.stats_mutex):
                self.stats['total_failed'] += len(batch)
            self._add_to_retry_queue(batch)
    
    def _add_to_retry_queue(self, records):
        """将记录添加到重试队列"""
        with QMutexLocker(self.retry_mutex):
            for record in records:
                if record.retry_count < MAX_RETRY_ATTEMPTS:
                    record.retry_count += 1
                    record.last_retry = datetime.now()
                    self.retry_queue.append(record)
    
    def _process_retry_queue(self):
        """处理重试队列"""
        if not self.running:
            return
            
        retry_records = []
        with QMutexLocker(self.retry_mutex):
            current_time = datetime.now()
            
            # 检查需要重试的记录
            while self.retry_queue:
                record = self.retry_queue.popleft()
                
                # 检查是否到了重试时间
                if (current_time - record.last_retry).total_seconds() >= 20:
                    if record.retry_count < MAX_RETRY_ATTEMPTS:
                        retry_records.append(record)
                    else:
                        logging.warning(f"记录重试次数已达上限，放弃: {record.chip_id}")
                else:
                    # 还没到重试时间，放回队列
                    self.retry_queue.append(record)
                    break
        
        # 将重试记录重新加入上传队列
        if retry_records and self.running:
            with QMutexLocker(self.upload_mutex):
                self.upload_queue.extend(retry_records)
            logging.info(f"重新加入上传队列: {len(retry_records)} 条记录")
    
    def get_stats(self):
        """获取统计信息"""
        with QMutexLocker(self.stats_mutex):
            with QMutexLocker(self.upload_mutex):
                self.stats['queue_size'] = len(self.upload_queue)
            with QMutexLocker(self.retry_mutex):
                self.stats['retry_size'] = len(self.retry_queue)
            return self.stats.copy()
    
    def clear_queues(self):
        """清空所有队列"""
        with QMutexLocker(self.upload_mutex):
            cleared_upload = len(self.upload_queue)
            self.upload_queue.clear()
            
        with QMutexLocker(self.retry_mutex):
            cleared_retry = len(self.retry_queue)
            self.retry_queue.clear()
            
        logging.info(f"清空队列 - 上传队列: {cleared_upload}, 重试队列: {cleared_retry}")
        return cleared_upload + cleared_retry

    def flush_remaining_data(self):
        """手动刷新所有剩余数据（用于外部调用）"""
        logging.info("手动刷新剩余数据...")

        # 处理剩余的上传队列
        queue_size = 0
        with QMutexLocker(self.upload_mutex):
            queue_size = len(self.upload_queue)

        if queue_size > 0:
            logging.info(f"处理剩余上传队列: {queue_size} 条")
            self._process_upload_batch()

        # 处理剩余的状态更新
        buffer_size = 0
        with QMutexLocker(self.status_buffer_mutex):
            buffer_size = len(self.status_update_buffer)

        if buffer_size > 0:
            logging.info(f"处理剩余状态更新: {buffer_size} 条")
            self._flush_status_updates()

        if queue_size == 0 and buffer_size == 0:
            logging.info("没有剩余数据需要处理")

    def _process_all_remaining_data(self):
        """处理所有剩余的上传数据（停止时使用）"""
        total_processed = 0
        batch_count = 0

        logger.info("🔄 开始循环处理剩余数据...")

        # 循环处理直到队列为空
        while True:
            batch = []
            current_queue_size = 0

            with QMutexLocker(self.upload_mutex):
                current_queue_size = len(self.upload_queue)
                if not self.upload_queue:
                    break

                # 每次处理一批数据
                batch_size = min(len(self.upload_queue), API_BATCH_SIZE)
                for _ in range(batch_size):
                    if self.upload_queue:
                        batch.append(self.upload_queue.popleft())

            if not batch:
                logger.info("📭 队列已空，停止处理")
                break

            batch_count += 1
            logger.info(f"📦 处理第 {batch_count} 批: {len(batch)} 条记录 (队列剩余: {current_queue_size - len(batch)})")

            # 提交到线程池处理
            try:
                self.api_executor.submit(self._upload_batch, batch)
                total_processed += len(batch)
                logger.info(f"✅ 第 {batch_count} 批已提交到线程池")
            except Exception as e:
                logger.error(f"❌ 提交第 {batch_count} 批异常: {str(e)}")
                # 如果线程池有问题，尝试同步处理
                try:
                    logger.info(f"🔄 同步处理第 {batch_count} 批: {len(batch)} 条")
                    self._upload_batch(batch)
                    total_processed += len(batch)
                    logger.info(f"✅ 第 {batch_count} 批同步处理完成")
                except Exception as sync_e:
                    logger.error(f"❌ 第 {batch_count} 批同步处理也失败: {str(sync_e)}")
                    break

        logger.info(f"🎯 剩余数据处理完成: 总共 {batch_count} 批, {total_processed} 条记录")

    def _handle_overflow_record(self, record):
        """处理队列溢出的记录"""
        try:
            # 方案1：写入临时文件
            import os
            import json
            from datetime import datetime

            overflow_dir = "overflow_data"
            if not os.path.exists(overflow_dir):
                os.makedirs(overflow_dir)

            # 按日期创建文件
            date_str = datetime.now().strftime("%Y%m%d")
            overflow_file = os.path.join(overflow_dir, f"overflow_{date_str}.jsonl")

            # 将记录写入文件
            record_data = {
                'chip_id': record.chip_id,
                'device_id': record.device_id,
                'scan_time': record.scan_time,
                'overflow_time': datetime.now().isoformat()
            }

            with open(overflow_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(record_data, ensure_ascii=False) + '\n')

            logger.info(f"溢出记录已保存到文件: {record.chip_id}")

        except Exception as e:
            logger.error(f"处理溢出记录失败: {str(e)}")

    def _monitor_queue_health(self):
        """监控队列健康状态并自动优化"""
        if not self.running:
            return

        try:
            with QMutexLocker(self.upload_mutex):
                queue_size = len(self.upload_queue)
                queue_usage = queue_size / API_MAX_QUEUE_SIZE

            with QMutexLocker(self.retry_mutex):
                retry_size = len(self.retry_queue)

            # 队列使用率监控
            if queue_usage > 0.8:  # 超过80%使用率
                logger.warning(f"上传队列使用率过高: {queue_usage:.1%} ({queue_size}/{API_MAX_QUEUE_SIZE})")

                # 自动优化：增加处理频率
                if self.batch_timer.interval() > 200:
                    self.batch_timer.setInterval(200)  # 减少到200ms
                    logger.info("自动优化：减少批量处理间隔到200ms")

                # 触发额外的批量处理
                self._process_upload_batch()

            elif queue_usage > 0.9:  # 超过90%使用率，紧急处理
                logger.error(f"上传队列接近满载: {queue_usage:.1%}")

                # 紧急处理：连续处理多批
                for _ in range(3):
                    self._process_upload_batch()

            elif queue_usage < 0.3:  # 使用率低于30%，恢复正常间隔
                if self.batch_timer.interval() < API_BATCH_INTERVAL:
                    self.batch_timer.setInterval(API_BATCH_INTERVAL)
                    logger.debug("队列压力正常，恢复正常处理间隔")

            # 重试队列监控
            if retry_size > 1000:
                logger.warning(f"重试队列积压严重: {retry_size} 条记录")

            # 记录队列状态
            logger.debug(f"队列状态 - 上传: {queue_size}, 重试: {retry_size}, 使用率: {queue_usage:.1%}")

        except Exception as e:
            logger.error(f"队列监控异常: {str(e)}")

    def _buffer_status_updates(self, records):
        """将状态更新加入缓冲区，减少UI信号频率"""
        with QMutexLocker(self.status_buffer_mutex):
            self.status_update_buffer.extend(records)

    def _flush_status_updates(self, force=False):
        """定时刷新状态更新缓冲区"""
        if not self.running and not force:
            return

        updates_to_send = []

        with QMutexLocker(self.status_buffer_mutex):
            if self.status_update_buffer:
                updates_to_send = self.status_update_buffer.copy()
                self.status_update_buffer.clear()

        # 发送批量状态更新
        if updates_to_send:
            logger.info(f"📤 发送状态更新信号: {len(updates_to_send)} 条记录 (force={force})")

            # 统计状态类型
            status_counts = {}
            for record in updates_to_send:
                status = getattr(record, 'status', '未知')
                status_counts[status] = status_counts.get(status, 0) + 1

            logger.info(f"📊 状态分布: {status_counts}")

            try:
                self.status_update_signal.emit(updates_to_send)
                logger.info(f"✅ 状态更新信号发送成功")
            except Exception as e:
                logger.error(f"❌ 状态更新信号发送失败: {str(e)}")
        else:
            if force:
                logger.info("📭 没有状态更新需要发送")
            else:
                logger.debug("📭 状态更新缓冲区为空")

# 全局上传管理器实例
_upload_manager = None

def get_upload_manager():
    """获取全局上传管理器实例"""
    global _upload_manager
    if _upload_manager is None:
        _upload_manager = UploadManager()
    return _upload_manager

def start_upload_manager():
    """启动全局上传管理器"""
    manager = get_upload_manager()
    manager.start()
    return manager

def stop_upload_manager():
    """停止全局上传管理器"""
    global _upload_manager
    if _upload_manager:
        # 记录停止前的状态
        stats = _upload_manager.get_stats()
        logging.info(f"停止上传管理器 - 队列大小: {stats.get('queue_size', 0)}, 重试队列: {stats.get('retry_size', 0)}")

        _upload_manager.stop()
        _upload_manager = None
        logging.info("全局上传管理器已清理")
